import { User } from '@/common/decorators/user.decorator';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Put,
  Request,
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';

import { Roles } from '../auth/decorators/roles.decorator';
import { SkipProfileCheck } from '../auth/decorators/skip-profile-check.decorator';
import { UserType } from '../auth/enums/user-type.enum';
import { AddressResponseDto, ApiResponseDto } from '../google-maps/dto/address-response.dto';
import { CreateUserAddressDto } from './dto/create-user-address.dto';
import { UpdateUserAddressDto } from './dto/update-user-address.dto';
import { UserAddressesService } from './user-addresses.service';

@ApiTags('Addresses')
@Controller('addresses')
@Roles({ userType: UserType.USER, role: '*' })
@SkipProfileCheck()
export class UserAddressesController {
  constructor(private readonly userAddressesService: UserAddressesService) {}

  @Get()
  @ApiOperation({ summary: 'Get all user addresses' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Addresses retrieved successfully',
    type: [AddressResponseDto],
  })
  async findAll(@User('id') userId: string): Promise<ApiResponseDto<AddressResponseDto[]>> {
    console.log(userId);
    const addresses = await this.userAddressesService.findAllByUser(userId);
    return {
      success: true,
      data: addresses,
      message: 'Addresses retrieved successfully',
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get address by ID' })
  @ApiParam({ name: 'id', description: 'Address ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Address retrieved successfully',
    type: AddressResponseDto,
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Address not found' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @User('id') userId: string,
  ): Promise<ApiResponseDto<AddressResponseDto>> {
    const address = await this.userAddressesService.findOneByUser(id, userId);
    return {
      success: true,
      data: address,
      message: 'Address retrieved successfully',
    };
  }

  @Post()
  @ApiOperation({ summary: 'Create new address' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Address created successfully',
    type: AddressResponseDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid input' })
  async create(
    @Body() createAddressDto: CreateUserAddressDto,
    @User('id') userId: string,
  ): Promise<ApiResponseDto<AddressResponseDto>> {
    const address = await this.userAddressesService.create(createAddressDto, userId);
    return {
      success: true,
      data: address,
      message: 'Address created successfully',
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update address' })
  @ApiParam({ name: 'id', description: 'Address ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Address updated successfully',
    type: AddressResponseDto,
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Address not found' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateAddressDto: UpdateUserAddressDto,
    @User('id') userId: string,
  ): Promise<ApiResponseDto<AddressResponseDto>> {
    const address = await this.userAddressesService.update(id, updateAddressDto, userId);
    return {
      success: true,
      data: address,
      message: 'Address updated successfully',
    };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete address' })
  @ApiParam({ name: 'id', description: 'Address ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Address deleted successfully',
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Address not found' })
  async remove(@Param('id', ParseUUIDPipe) id: string, @User('id') userId: string): Promise<ApiResponseDto<null>> {
    await this.userAddressesService.remove(id, userId);
    return {
      success: true,
      message: 'Address deleted successfully',
    };
  }

  @Put(':id/default')
  @ApiOperation({ summary: 'Set address as default' })
  @ApiParam({ name: 'id', description: 'Address ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Default address updated successfully',
    type: AddressResponseDto,
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Address not found' })
  async setDefault(
    @Param('id', ParseUUIDPipe) id: string,
    @User('id') userId: string,
  ): Promise<ApiResponseDto<AddressResponseDto>> {
    const address = await this.userAddressesService.setDefault(id, userId);
    return {
      success: true,
      data: address,
      message: 'Default address updated successfully',
    };
  }
}
