import { Module } from '@nestjs/common';

import { EmailModule } from './email/email.module';
import { FCMModule } from './fcm/fcm.module';
import { MicroserviceModule } from './microservice/microservice.module';
import { RedisModule } from './redis/redis.module';

@Module({
  imports: [EmailModule, FCMModule, MicroserviceModule, RedisModule],
  exports: [EmailModule, FCMModule, MicroserviceModule, RedisModule],
})
export class SharedModule {}
