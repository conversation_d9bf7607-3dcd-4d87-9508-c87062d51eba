import { IPaginationOptions, paginate, Pagination } from 'nestjs-typeorm-paginate';
import { In, Repository } from 'typeorm';

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { CreateRestaurantTagDto } from './dto/create-restaurant-tag.dto';
import { ListRestaurantTagDto } from './dto/list-restaurant-tag.dto';
import { UpdateRestaurantTagDto } from './dto/update-restaurant-tag.dto';
import { RestaurantTag } from './entities/restaurant-tag.entity';

@Injectable()
export class RestaurantTagsService {
  constructor(
    @InjectRepository(RestaurantTag)
    private readonly restaurantTagRepository: Repository<RestaurantTag>,
  ) {}

  async create(createRestaurantTagDto: CreateRestaurantTagDto) {
    const restaurantTag = this.restaurantTagRepository.create(createRestaurantTagDto);
    return this.restaurantTagRepository.save(restaurantTag);
  }

  async update(id: string, updateRestaurantTagDto: UpdateRestaurantTagDto): Promise<RestaurantTag> {
    const restaurantTag = await this.findOne(id);
    Object.assign(restaurantTag, updateRestaurantTagDto);

    return this.restaurantTagRepository.save(restaurantTag);
  }

  async remove(id: string) {
    const restaurantTag = await this.findOne(id);

    return this.restaurantTagRepository.softDelete(restaurantTag.id);
  }

  async findAll(listRestaurantTagDto: ListRestaurantTagDto): Promise<Pagination<RestaurantTag>> {
    const { name, restaurantId, page, limit } = listRestaurantTagDto;

    const querryBuilder = this.restaurantTagRepository.createQueryBuilder('restaurantTag');

    if (name) {
      querryBuilder.andWhere('restaurantTag.name ILIKE :name', { name: `%${name}%` });
    }

    if (restaurantId) {
      querryBuilder.andWhere('restaurantTag.restaurantId = :restaurantId', { restaurantId });
    }

    const options: IPaginationOptions = { page, limit };

    return paginate<RestaurantTag>(querryBuilder, options);
  }

  async findOne(id: string): Promise<RestaurantTag> {
    const restaurantTag = await this.restaurantTagRepository.findOne({ where: { id } });

    if (!restaurantTag) {
      throw new NotFoundException('Not found restaurant tag');
    }
    return restaurantTag;
  }

  async getListById(tagIds: string[]) {
    return this.restaurantTagRepository.find({ where: { id: In(tagIds) } });
  }
}
