import { Is<PERSON>rray, IsOptional, IsString, IsUUID } from 'class-validator';

import { IsValidS3Url } from '@/common/validators/s3-url.validator';
import { FolderType } from '@/modules/upload/upload.constants';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateRestaurantDto {
  @ApiProperty({ description: 'Name of the restaurant', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: 'Avatar image URL of the restaurant', required: false })
  @IsOptional()
  @IsString()
  @IsValidS3Url(FolderType.RESTAURANT_AVATAR)
  avatarImg?: string;

  @ApiProperty({ description: 'Background image URL of the restaurant', required: false })
  @IsOptional()
  @IsString()
  @IsValidS3Url(FolderType.RESTAURANT_BANNER)
  backgroundImg?: string;

  @ApiProperty({ description: 'Price range of the restaurant', required: false })
  @IsOptional()
  @IsString()
  priceRange?: string;

  @ApiProperty({ description: 'ID of the brand', required: false })
  @IsOptional()
  @IsUUID()
  brandId?: string;

  @ApiProperty({ description: 'Tags of the restaurant', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  tagIds?: string[];
}
