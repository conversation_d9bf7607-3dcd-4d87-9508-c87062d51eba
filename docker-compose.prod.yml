version: '3.8'

services:
  api:
    image: ${ECR_URI}:${IMAGE_TAG:-latest}
    container_name: anhbeo-api
    restart: always
    pull_policy: always
    ports:
      - "${PORT:-3000}:${PORT:-3000}"
    environment:
      - NODE_ENV=production
    volumes:
      - .env:/usr/src/app/.env
      - ${FCM_SERVICE_ACCOUNT_PATH}:/usr/src/app/${FCM_SERVICE_ACCOUNT_PATH}.json
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - anhbeo-network

  postgres:
    image: postgres:16.8-alpine
    container_name: anhbeo-postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - anhbeo-network

  redis:
    image: redis:7.4.3-alpine
    container_name: anhbeo-redis
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - anhbeo-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

# docker network create anhbeo-network 2>/dev/null || echo "Network anhbeo-network already exists"
networks:
  anhbeo-network:
    driver: bridge
    external: true
