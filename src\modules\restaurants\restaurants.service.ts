import { Pagination } from 'nestjs-typeorm-paginate';
import { DataSource, Repository } from 'typeorm';

import { paginateQueryBuilder } from '@/helpers/queryBuilder';
import { generateCode } from '@/helpers/string';
import { BrandsService } from '@/modules/brands/brands.service';
import { MerchantStaffService } from '@/modules/merchant-staff/merchant-staff.service';
import { RestaurantTagsService } from '@/modules/restaurant-tags/restaurant-tags.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { MerchantStaff } from '../merchant-staff/entities/merchant-staff.entity';
import { CreateRestaurantDto } from './dtos/create-restaurant.dto';
import { FilterRestaurantDto } from './dtos/filter-restaurant.dto';
import { ListRestaurantDto } from './dtos/list-restaurant.dto';
import { UpdateRestaurantDto } from './dtos/update-restaurant.dto';
import { Restaurant } from './entities/restaurant.entity';

@Injectable()
export class RestaurantsService {
  constructor(
    @InjectRepository(Restaurant)
    private restaurantRepository: Repository<Restaurant>,

    private dataSource: DataSource,
    private brandsService: BrandsService,
    private restaurantTagsService: RestaurantTagsService,
    private merchantStaffService: MerchantStaffService,
  ) {}

  async create(createRestaurantDto: CreateRestaurantDto, ownerId: string | null): Promise<Restaurant> {
    const { tagIds, brandId } = createRestaurantDto;

    // For merchant users, verify they have access to the brand they're trying to create a restaurant for
    // Check if the brand belongs to a merchant account owned by this user
    await this.brandsService.verifyAccessBrand(brandId, ownerId);
    let merchantStaff: MerchantStaff = new MerchantStaff();
    let savedRestaurantId: string = '';
    await this.dataSource.transaction(async (manager) => {
      // Create restaurant entity
      const restaurant = manager.create(Restaurant, {
        ...createRestaurantDto,
        code: generateCode(3),
      });

      // Handle tag relationship
      await this.handleTagRelationship(restaurant, tagIds);

      // Save the restaurant with relationships using transaction manager
      const savedRestaurant = await manager.save(Restaurant, restaurant);

      // Create merchant staff for this restaurant within the same transaction
      merchantStaff = await this.merchantStaffService.create(savedRestaurant.id, manager);
      savedRestaurantId = savedRestaurant.id;
    });

    // Return restaurant with merchant staff info
    const restaurantWithDetails = await this.findOne(savedRestaurantId, ownerId);

    restaurantWithDetails['merchantStaff'] = {
      id: merchantStaff.id,
      username: merchantStaff.username,
      banned: merchantStaff.banned,
    };

    return restaurantWithDetails;
  }

  async findAll(listRestaurantDto: ListRestaurantDto, ownerId: string | null) {
    const { name, priceRange, minStarRated, brandId, restaurantTagIds, rating, page, limit } = listRestaurantDto;

    const queryBuilder = this.restaurantRepository.createQueryBuilder('restaurant');

    // Add relations to get access to brand and merchant account data
    queryBuilder.leftJoinAndSelect('restaurant.brand', 'brand');
    queryBuilder.leftJoinAndSelect('restaurant.tags', 'tags');

    if (name) {
      queryBuilder.andWhere('restaurant.name ILIKE :name', { name: `%${name}%` });
    }

    if (priceRange) {
      queryBuilder.andWhere('restaurant.priceRange = :priceRange', { priceRange });
    }

    if (minStarRated !== undefined) {
      queryBuilder.andWhere('restaurant.starRated >= :minStarRated', { minStarRated });
    }

    // For merchant users, only show restaurants associated with brands they have access to
    if (ownerId) {
      queryBuilder
        .leftJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    // Apply brandId filter if provided
    if (brandId) {
      queryBuilder.andWhere('restaurant.brandId = :brandId', { brandId });
    }

    if (restaurantTagIds?.length) {
      queryBuilder.andWhere('tag.id IN (:...restaurantTagIds)', { restaurantTagIds }); // Filter by tag ID
    }

    if (rating === true) {
      queryBuilder.orderBy('restaurant.starRated', 'DESC');
    } else {
      queryBuilder.orderBy('restaurant.updatedAt', 'DESC');
    }

    return paginateQueryBuilder(queryBuilder, { page, limit });
  }

  async findOne(id: string, ownerId: string | null): Promise<Restaurant> {
    // Create query builder to handle user-specific conditions
    const queryBuilder = this.restaurantRepository
      .createQueryBuilder('restaurant')
      .leftJoinAndSelect('restaurant.brand', 'brand')
      .leftJoinAndSelect('restaurant.tags', 'tags')
      .where('restaurant.id = :id', { id });

    // For merchant users, only allow access to restaurants associated with brands they have access to
    if (ownerId) {
      queryBuilder
        .leftJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    const restaurant = await queryBuilder.getOne();

    if (!restaurant) {
      throw new NotFoundException(`Restaurant with ID ${id} not found or you don't have access to it`);
    }

    return restaurant;
  }

  async update(id: string, updateRestaurantDto: UpdateRestaurantDto, ownerId: string | null): Promise<Restaurant> {
    const restaurant = await this.findOne(id, ownerId);
    return await this.dataSource.transaction(async (manager) => {
      // If we get here, the user has permission to update the restaurant
      Object.assign(restaurant, updateRestaurantDto);

      // Handle tag relationship
      await this.handleTagRelationship(restaurant, updateRestaurantDto.tagIds);

      return manager.save(Restaurant, restaurant);
    });
  }

  async activate(id: string, ownerId: string | null): Promise<Restaurant> {
    const restaurant = await this.findOne(id, ownerId);

    // If we get here, the user has permission to activate the restaurant
    restaurant.activeAt = new Date();

    return this.restaurantRepository.save(restaurant);
  }

  async deactivate(id: string, ownerId: string | null): Promise<Restaurant> {
    const restaurant = await this.findOne(id, ownerId);

    // If we get here, the user has permission to deactivate the restaurant
    restaurant.activeAt = null;

    return this.restaurantRepository.save(restaurant);
  }

  /**
   * Verify that a restaurant exists and the user has access to it
   * @param restaurantId The ID of the restaurant to verify access to
   * @param ownerId The ID of the user (null for admin users)
   * @throws NotFoundException if the restaurant doesn't exist or the user doesn't have access to it
   */
  async verifyAccessRestaurant(restaurantId: string, ownerId: string | null) {
    const queryBuilder = this.restaurantRepository
      .createQueryBuilder('restaurant')
      .where('restaurant.id = :restaurantId', { restaurantId });

    // For merchant users, only allow access to restaurants they have access to
    if (ownerId) {
      queryBuilder
        .leftJoin('restaurant.brand', 'brand')
        .leftJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    const restaurant = await queryBuilder.getOne();

    if (!restaurant) {
      throw new NotFoundException(`Restaurant with ID ${restaurantId} not found or you don't have access to it`);
    }

    return restaurant;
  }

  // user API
  async userFindAll(filterRestaurantDto: FilterRestaurantDto): Promise<Pagination<Restaurant>> {
    const { name, priceRange, minStarRated, brandId, restaurantTagIds, page, limit, sortBy, sort } =
      filterRestaurantDto;

    const queryBuilder = this.restaurantRepository.createQueryBuilder('restaurant');

    // Add relations to get access to brand and merchant account data
    queryBuilder
      .leftJoinAndSelect('restaurant.brand', 'brand')
      .leftJoinAndSelect('restaurant.tags', 'tags')
      .leftJoin('brand.merchantAccount', 'merchantAccount')
      .andWhere('restaurant.activeAt IS NOT NULL')
      .andWhere('brand.activeAt IS NOT NULL')
      .andWhere('merchantAccount.activeAt IS NOT NULL');

    if (name) {
      queryBuilder.andWhere('restaurant.name ILIKE :name', { name: `%${name}%` });
    }

    if (priceRange) {
      queryBuilder.andWhere('restaurant.priceRange = :priceRange', { priceRange });
    }

    if (minStarRated !== undefined) {
      queryBuilder.andWhere('restaurant.starRated >= :minStarRated', { minStarRated });
    }

    // Apply brandId filter if provided
    if (brandId) {
      queryBuilder.andWhere('restaurant.brandId = :brandId', { brandId });
    }

    if (restaurantTagIds?.length) {
      queryBuilder.andWhere('tag.id IN (:...restaurantTagIds)', { restaurantTagIds }); // Filter by tag ID
    }

    queryBuilder.orderBy(`restaurant.${sortBy}`, sort);

    return paginateQueryBuilder(queryBuilder, { page, limit });
  }

  async userFindOne(id: string): Promise<Restaurant> {
    const queryBuilder = this.restaurantRepository.createQueryBuilder('restaurant');

    queryBuilder
      .select([
        'restaurant.id',
        'restaurant.name',
        'restaurant.avatarImg',
        'restaurant.backgroundImg',
        'restaurant.priceRange',
        'restaurant.starRated',
        'restaurant.totalReviews',
        'restaurant.activeAt',
      ])
      .where('restaurant.id = :id', { id })
      .leftJoin('restaurant.brand', 'brand')
      .leftJoin('brand.merchantAccount', 'merchantAccount')
      .leftJoinAndSelect('restaurant.tags', 'tags')
      .addSelect(['brand.id', 'brand.name'])
      .andWhere('restaurant.activeAt IS NOT NULL')
      .andWhere('brand.activeAt IS NOT NULL')
      .andWhere('merchantAccount.activeAt IS NOT NULL')
      .leftJoin(
        'restaurant.menus',
        'menus',
        'menus.activeAt IS NOT NULL AND menus.id = (SELECT m.id FROM menus m WHERE m.restaurant_id = restaurant.id AND m.active_at IS NOT NULL ORDER BY m.active_at DESC LIMIT 1)',
      )
      .addSelect(['menus.id', 'menus.name', 'menus.activeAt'])
      .leftJoin('menus.mappingMenuSections', 'mappingMenuSections')
      .addSelect(['mappingMenuSections.position'])
      .leftJoin('mappingMenuSections.menuSection', 'menuSection')
      .addSelect(['menuSection.id', 'menuSection.publishedName', 'menuSection.viewType', 'menuSection.activeAt'])
      .leftJoinAndSelect('menuSection.availableSchedule', 'availableSchedule')
      .leftJoinAndSelect('menuSection.mappingMenuItems', 'mappingMenuItems')
      .leftJoin('mappingMenuItems.menuItem', 'menuItem')
      .addSelect([
        'menuItem.id',
        'menuItem.publishedName',
        'menuItem.description',
        'menuItem.basePrice',
        'menuItem.imageUrls',
        'menuItem.activeAt',
      ]);

    const restaurant = await queryBuilder.getOne();

    if (!restaurant) {
      throw new NotFoundException(`Restaurant not found`);
    }

    // Transform the result to have activeMenu instead of menus array
    if (restaurant.menus && restaurant.menus.length > 0) {
      restaurant.menu = restaurant.menus[0];
    }

    if (restaurant.menus) delete restaurant.menus;

    return restaurant;
  }

  async handleTagRelationship(restaurant: Restaurant, tagIds?: string[]) {
    // If tagIds is undefined, don't update menuItems
    if (tagIds === undefined) {
      return;
    }

    // If tagIds is an empty array, clear all tags
    if (tagIds.length === 0) {
      restaurant.tags = [];
      return;
    }

    // Find tags that match both the provided IDs and the brand ID
    const tags = await this.restaurantTagsService.getListById(tagIds);

    // Check if all tag IDs exist and belong to the brand
    if (tags.length !== tagIds.length) {
      const foundIds = tags.map((tag) => tag.id);
      const missingIds = tagIds.filter((id) => !foundIds.includes(id));

      // If there are IDs that don't exist at all
      if (missingIds.length > 0) {
        throw new NotFoundException(`The following tag IDs do not exist: ${missingIds.join(', ')}`);
      }
    }

    // Set the menu items relation
    restaurant.tags = tags;
  }
}
