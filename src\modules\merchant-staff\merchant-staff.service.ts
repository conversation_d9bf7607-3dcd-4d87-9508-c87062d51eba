import * as bcrypt from 'bcrypt';
import { IPaginationOptions, paginate, Pagination } from 'nestjs-typeorm-paginate';
import { EntityManager, Repository } from 'typeorm';

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { ListMerchantStaffDto } from './dtos/list-merchant-staff.dto';
import { UpdatePasswordDto } from './dtos/update-password.dto';
import { MerchantStaff } from './entities/merchant-staff.entity';

@Injectable()
export class MerchantStaffService {
  constructor(
    @InjectRepository(MerchantStaff)
    private merchantStaffRepository: Repository<MerchantStaff>,
  ) {}

  async create(restaurantId: string, manager: EntityManager): Promise<MerchantStaff> {
    // Create merchant staff for this restaurant within the same transaction
    const username = await this.generateUniqueUsername(manager);
    const defaultPassword = '123456';
    const hashedPassword = await bcrypt.hash(defaultPassword, 10);

    const merchantStaff = manager.create(MerchantStaff, {
      username,
      password: hashedPassword,
      restaurantId,
      activeAt: new Date(),
    });

    return await manager.save(MerchantStaff, merchantStaff);
  }

  getMe(id: string) {
    return this.merchantStaffRepository.findOne({
      where: { id },
      select: {
        id: true,
        username: true,
        role: true,
        banned: true,
        restaurantId: true,
        restaurant: {
          name: true,
          brand: {
            name: true,
          },
        },
      },
      relations: ['restaurant', 'restaurant.brand'],
    });
  }

  async findAll(
    listMerchantStaffDto: ListMerchantStaffDto,
    ownerId: string | null,
  ): Promise<Pagination<MerchantStaff>> {
    const { restaurantId, brandId, page, limit } = listMerchantStaffDto;

    const queryBuilder = this.merchantStaffRepository.createQueryBuilder('merchantStaff');
    queryBuilder.leftJoinAndSelect('merchantStaff.restaurant', 'restaurant');

    if (restaurantId) {
      queryBuilder.andWhere('merchantStaff.restaurantId = :restaurantId', { restaurantId });
    }

    if (brandId) {
      queryBuilder.andWhere('restaurant.brandId = :brandId', { brandId });
    }

    if (ownerId) {
      queryBuilder.leftJoin('restaurant.brand', 'brand');
      queryBuilder.leftJoin('brand.merchantAccount', 'merchantAccount');
      queryBuilder.andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    queryBuilder.orderBy('merchantStaff.createdAt', 'DESC');

    const options: IPaginationOptions = { page, limit };
    return paginate<MerchantStaff>(queryBuilder, options);
  }

  async findOne(id: string, ownerId: string | null): Promise<MerchantStaff> {
    const queryBuilder = this.merchantStaffRepository.createQueryBuilder('merchantStaff');
    queryBuilder.leftJoinAndSelect('merchantStaff.restaurant', 'restaurant').where('merchantStaff.id = :id', { id });

    if (ownerId) {
      queryBuilder.leftJoin('restaurant.brand', 'brand');
      queryBuilder.leftJoin('brand.merchantAccount', 'merchantAccount');
      queryBuilder.andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    const merchantStaff = await queryBuilder.getOne();

    if (!merchantStaff) {
      throw new NotFoundException(`Merchant staff with ID ${id} not found`);
    }

    return merchantStaff;
  }

  async findById(id: string): Promise<MerchantStaff | null> {
    return this.merchantStaffRepository.findOne({ where: { id } });
  }

  async findByUsername(username: string): Promise<MerchantStaff | null> {
    return this.merchantStaffRepository.findOne({ where: { username } });
  }

  async updatePassword(id: string, updatePasswordDto: UpdatePasswordDto, ownerId: string | null): Promise<boolean> {
    const { newPassword } = updatePasswordDto;
    const merchantStaff = await this.findOne(id, ownerId);

    const hashedPassword = await bcrypt.hash(newPassword, 10);
    merchantStaff.password = hashedPassword;

    return true;
  }

  private async generateUniqueUsername(manager: EntityManager): Promise<string> {
    let username: string;
    let isUnique = false;

    while (!isUnique) {
      // Generate 10 random digits
      const randomDigits = Math.floor(Math.random() * 10000000000)
        .toString()
        .padStart(10, '0');
      username = `RES${randomDigits}`;

      // Check if username already exists using transaction manager
      const existingStaff = await manager.findOne(MerchantStaff, {
        where: { username },
      });

      if (!existingStaff) {
        isUnique = true;
      }
    }

    return username!;
  }

  async ban(id: string, ownerId: string | null): Promise<MerchantStaff> {
    const merchantUser = await this.findOne(id, ownerId);

    merchantUser.banned = true;

    return this.merchantStaffRepository.save(merchantUser);
  }

  async unban(id: string, ownerId: string | null): Promise<MerchantStaff> {
    const merchantUser = await this.findOne(id, ownerId);

    merchantUser.banned = false;

    return this.merchantStaffRepository.save(merchantUser);
  }
}
