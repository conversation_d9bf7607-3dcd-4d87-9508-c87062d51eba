import { Column, Entity, Index, JoinColumn, JoinTable, ManyToMany, ManyToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { ColumnNumericTransformer } from '@/common/transformers/column-numeric.transformer';
import { Brand } from '@/modules/brands/entities/brand.entity';
import { Menu } from '@/modules/menus/entities/menu.entity';
import { RestaurantTag } from '@/modules/restaurant-tags/entities/restaurant-tag.entity';

@Entity('restaurants')
@Index(['brandId', 'activeAt']) // Brand filtering with active status
export class Restaurant extends BaseEntity {
  @Index()
  @Column({ type: 'varchar' })
  name: string;

  @Index({ unique: true })
  @Column({ name: 'code', type: 'varchar' })
  code: string;

  @Column({ name: 'avatar_img', type: 'varchar' })
  avatarImg: string;

  @Column({ name: 'background_img', type: 'varchar' })
  backgroundImg: string;

  @Index()
  @Column({ name: 'price_range', nullable: true, type: 'varchar' })
  priceRange?: string | null;

  @Index()
  @Column({
    name: 'star_rated',
    type: 'decimal',
    precision: 3,
    scale: 1,
    default: 0,
    transformer: new ColumnNumericTransformer(),
  })
  starRated: number;

  @Index()
  @Column({ name: 'total_reviews', type: 'integer', default: 0 })
  totalReviews: number;

  @Column({ name: 'brand_id', type: 'uuid' })
  brandId: string;

  @Column({ name: 'active_at', nullable: true, type: 'timestamptz' })
  activeAt?: Date | null;

  @ManyToOne(() => Brand, (brand) => brand.restaurants)
  @JoinColumn({ name: 'brand_id' })
  brand: WrapperType<Brand>;

  @ManyToMany(() => RestaurantTag, (restaurantTag) => restaurantTag.restaurants)
  @JoinTable({
    name: 'mapping_restaurant_tags_restaurants',
    joinColumn: {
      name: 'restaurant_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'restaurant_tag_id',
      referencedColumnName: 'id',
    },
  })
  tags: WrapperType<RestaurantTag>[];

  @OneToMany(() => Menu, (menu) => menu.restaurant)
  menus?: WrapperType<Menu>[];

  menu?: WrapperType<Menu>;
}
