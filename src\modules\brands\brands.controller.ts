import { Pagination } from 'nestjs-typeorm-paginate';

import { UserMerchantId } from '@/common/decorators/user.decorator';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { BrandsService } from './brands.service';
import { CreateBrandDto } from './dtos/create-brand.dto';
import { ListBrandDto } from './dtos/list-brand.dto';
import { UpdateBrandDto } from './dtos/update-brand.dto';
import { Brand } from './entities/brand.entity';

@ApiTags('Brands')
@Controller('brands')
@Roles({ userType: UserType.MERCHANT_USER, role: '*' })
export class BrandsController {
  constructor(private readonly brandsService: BrandsService) {}

  @Post()
  create(@Body() createBrandDto: CreateBrandDto, @UserMerchantId() ownerId: string | null): Promise<Brand> {
    return this.brandsService.create(createBrandDto, ownerId);
  }

  @Get()
  findAll(@Query() listBrandDto: ListBrandDto, @UserMerchantId() ownerId: string | null): Promise<Pagination<Brand>> {
    return this.brandsService.findAll(listBrandDto, ownerId);
  }

  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<Brand> {
    return this.brandsService.findOne(id, ownerId);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateBrandDto: UpdateBrandDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Brand> {
    return this.brandsService.update(id, updateBrandDto, ownerId);
  }

  @Put('activate/:id')
  activate(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<Brand> {
    return this.brandsService.activate(id, ownerId);
  }

  @Put('deactivate/:id')
  deactivate(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<Brand> {
    return this.brandsService.deactivate(id, ownerId);
  }
}
