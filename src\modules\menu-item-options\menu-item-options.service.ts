import { isNil } from 'lodash';
import { IPaginationOptions, paginate, Pagination } from 'nestjs-typeorm-paginate';
import { DataSource, EntityManager, In, Repository } from 'typeorm';

import { GroupItemWithPriceDto } from '@/common/dtos/group-item-with-price.dto';
import { NameValidationHelper } from '@/common/helpers/name-validation.helper';
import { IngredientsService } from '@/modules/ingredients/ingredients.service';
import { MappingMenuItemOptionGroupMenuItemOption } from '@/modules/menu-item-option-groups/entities/mapping-menu-item-option-group-menu-item-option.entity';
import { MenuItemOptionGroup } from '@/modules/menu-item-option-groups/entities/menu-item-option-group.entity';
import { RestaurantsService } from '@/modules/restaurants/restaurants.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { ListAllMenuItemDto } from '../menu-items/dtos/list-menu-item.dto';
import { CreateMenuItemOptionDto } from './dtos/create-menu-item-option.dto';
import { ListMenuItemOptionDto } from './dtos/list-menu-item-option.dto';
import { UpdateMenuItemOptionDto } from './dtos/update-menu-item-option.dto';
import { MenuItemOption } from './entities/menu-item-option.entity';

@Injectable()
export class MenuItemOptionsService {
  constructor(
    @InjectRepository(MenuItemOption)
    private menuItemOptionRepository: Repository<MenuItemOption>,

    private ingredientsService: IngredientsService,
    private restaurantsService: RestaurantsService,
    private dataSource: DataSource,
  ) {}

  async create(
    createMenuItemOptionDto: CreateMenuItemOptionDto,
    ownerId: string | null,
    entityManager?: EntityManager,
  ): Promise<MenuItemOption> {
    // Verify restaurant exists and user has access to it
    await this.restaurantsService.verifyAccessRestaurant(createMenuItemOptionDto.restaurantId, ownerId);

    // Validate unique names using helper
    await NameValidationHelper.validateUniqueNames(
      createMenuItemOptionDto.restaurantId,
      createMenuItemOptionDto.internalName,
      createMenuItemOptionDto.publishedName,
      undefined,
      this.checkNameExists.bind(this),
    );

    // Extract ingredientIds and menuItemOptionGroupIds from DTO
    const { ingredientIds, menuItemOptionGroupIds, scheduleActiveAt, isActive, ...menuItemOptionData } =
      createMenuItemOptionDto;

    let id: string = '';

    const handleCreateMenuItemOption = async (entityManager) => {
      // Create menu item option
      const menuItemOption = entityManager.create(MenuItemOption, menuItemOptionData);

      // Handle isActive and scheduleActiveAt logic
      if (isActive) {
        menuItemOption.activeAt = new Date();
        menuItemOption.scheduleActiveAt = null;
      } else if (scheduleActiveAt) {
        menuItemOption.scheduleActiveAt = new Date(scheduleActiveAt);
      }

      // Handle ingredient relationships
      await this.ingredientsService.handleIngredientRelationship(menuItemOption, ingredientIds);

      // Save the updated entity with relationships
      const savedMenuItemOption = await entityManager.save(menuItemOption);

      // Handle menu item option group relationships
      await this.handleMenuItemOptionGroupRelationship(savedMenuItemOption, menuItemOptionGroupIds, entityManager);

      id = savedMenuItemOption.id;
    };

    if (entityManager) {
      await handleCreateMenuItemOption(entityManager);
    } else {
      await this.dataSource.transaction(handleCreateMenuItemOption);
    }

    return this.findOne(id, ownerId, true, entityManager);
  }

  async getAllMenuItemOptions(listMenuItemDto: ListAllMenuItemDto, ownerId: string | null) {
    const { restaurantId } = listMenuItemDto;

    const queryBuilder = this.menuItemOptionRepository.createQueryBuilder('menuItemOption');

    if (restaurantId) {
      queryBuilder.andWhere('menuItemOption.restaurantId = :restaurantId', { restaurantId });
    }

    // For merchant users, only show menu items they have access to
    if (ownerId) {
      queryBuilder
        .leftJoin('menuItemOption.restaurant', 'restaurant')
        .leftJoin('restaurant.brand', 'brand')
        .leftJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    queryBuilder
      .leftJoinAndSelect('menuItemOption.mappingMenuItemOptionGroups', 'mappingMenuItemOptionGroups')
      .leftJoinAndSelect('mappingMenuItemOptionGroups.menuItemOptionGroup', 'menuItemOptionGroup');
    return queryBuilder.getMany();
  }

  async findAll(
    listMenuItemOptionDto: ListMenuItemOptionDto,
    ownerId: string | null,
  ): Promise<Pagination<MenuItemOption>> {
    const { internalName, publishedName, minBasePrice, maxBasePrice, restaurantId, page, limit } =
      listMenuItemOptionDto;

    const queryBuilder = this.menuItemOptionRepository.createQueryBuilder('menuItemOption');

    if (restaurantId) {
      queryBuilder.andWhere('menuItemOption.restaurantId = :restaurantId', { restaurantId });
    }

    // For merchant users, only show menu item options they have access to
    if (ownerId) {
      queryBuilder
        .leftJoin('menuItemOption.restaurant', 'restaurant')
        .leftJoin('restaurant.brand', 'brand')
        .leftJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    // Apply filters
    if (minBasePrice !== undefined) {
      queryBuilder.andWhere('menuItemOption.basePrice >= :minBasePrice', { minBasePrice });
    }

    if (maxBasePrice !== undefined) {
      queryBuilder.andWhere('menuItemOption.basePrice <= :maxBasePrice', { maxBasePrice });
    }

    if (internalName) {
      queryBuilder.andWhere('menuItemOption.internalName ILIKE :internalName', { internalName: `%${internalName}%` });
    }

    if (publishedName) {
      queryBuilder.andWhere('menuItemOption.publishedName ILIKE :publishedName', {
        publishedName: `%${publishedName}%`,
      });
    }

    // Order by most recently updated
    queryBuilder.orderBy('menuItemOption.updatedAt', 'DESC');

    const options: IPaginationOptions = { page, limit };

    return paginate<MenuItemOption>(queryBuilder, options);
  }

  async findOne(
    id: string,
    ownerId: string | null,
    relations: boolean = true,
    entityManager?: EntityManager,
  ): Promise<MenuItemOption> {
    const queryBuilder = entityManager
      ? entityManager.createQueryBuilder(MenuItemOption, 'menuItemOption')
      : this.menuItemOptionRepository.createQueryBuilder('menuItemOption');

    queryBuilder
      .where('menuItemOption.id = :id', { id })
      .leftJoinAndSelect('menuItemOption.ingredients', 'ingredients')
      .leftJoinAndSelect('menuItemOption.restaurant', 'restaurant');

    if (relations) {
      queryBuilder
        .leftJoinAndSelect('menuItemOption.mappingMenuItemOptionGroups', 'mappingMenuItemOptionGroups')
        .leftJoinAndSelect('mappingMenuItemOptionGroups.menuItemOptionGroup', 'menuItemOptionGroup');
    }
    // For merchant users, only allow access to menu item options they have access to
    if (ownerId) {
      queryBuilder
        .leftJoin('restaurant.brand', 'brand')
        .leftJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    const menuItemOption = await queryBuilder.getOne();

    if (!menuItemOption) {
      throw new NotFoundException(`Menu item option with ID ${id} not found or you don't have access to it`);
    }

    return menuItemOption;
  }

  async softDelete(id: string, ownerId: string | null, entityManager?: EntityManager) {
    const menuItemOption = await this.findOne(id, ownerId, false);

    const handleDelete = async (manager: EntityManager) => {
      // Soft delete mapping relations first
      await manager.delete(MappingMenuItemOptionGroupMenuItemOption, { menuItemOptionId: menuItemOption.id });

      // Then soft delete the menu item option
      return manager.softRemove(menuItemOption);
    };

    if (entityManager) {
      return handleDelete(entityManager);
    } else {
      return this.dataSource.transaction(handleDelete);
    }
  }

  async update(
    id: string,
    updateMenuItemOptionDto: UpdateMenuItemOptionDto,
    ownerId: string | null,
  ): Promise<MenuItemOption> {
    const menuItemOption = await this.findOne(id, ownerId);

    // Validate unique names using helper
    await NameValidationHelper.validateUniqueNames(
      menuItemOption.restaurantId,
      updateMenuItemOptionDto.internalName,
      updateMenuItemOptionDto.publishedName,
      id,
      this.checkNameExists.bind(this),
    );

    // Extract ingredientIds and menuItemOptionGroupIds from DTO
    const { ingredientIds, menuItemOptionGroupIds, scheduleActiveAt, isActive, ...menuItemOptionData } =
      updateMenuItemOptionDto;

    // Update basic properties
    Object.assign(menuItemOption, menuItemOptionData);

    await this.dataSource.transaction(async (entityManager) => {
      const menuItemOptionWasActive = !isNil(menuItemOption.activeAt);

      // Handle isActive and scheduleActiveAt logic
      if (!isNil(isActive) && menuItemOptionWasActive !== isActive) {
        menuItemOption.activeAt = isActive ? new Date() : null;
      }

      if (!menuItemOption.activeAt && scheduleActiveAt !== undefined) {
        menuItemOption.scheduleActiveAt = scheduleActiveAt ? new Date(scheduleActiveAt) : null;
      }

      // Handle ingredient relationships
      await this.ingredientsService.handleIngredientRelationship(menuItemOption, ingredientIds);

      // Handle menu item option group relationships
      await this.handleMenuItemOptionGroupRelationship(menuItemOption, menuItemOptionGroupIds, entityManager);

      // Save the updated entity
      await entityManager.save(MenuItemOption, menuItemOption);
    });

    return this.findOne(id, ownerId);
  }

  async getListByRestaurantId(restaurantId: string, menuItemOptionIds: string[]) {
    return this.menuItemOptionRepository.find({
      where: { id: In(menuItemOptionIds), restaurantId },
    });
  }

  /**
   * Helper method to handle option group relationships for options (add option to option groups)
   * @param menuItemOption The option entity to update
   * @param menuItemOptionGroups Array of option group IDs to link, or undefined to skip updating
   */
  private async handleMenuItemOptionGroupRelationship(
    menuItemOption: MenuItemOption,
    menuItemOptionGroups: GroupItemWithPriceDto[] | undefined,
    entityManager: EntityManager,
  ): Promise<void> {
    // If menuItemOptionGroupIds is undefined, don't update option groups
    if (menuItemOptionGroups === undefined) {
      return;
    }

    if (!menuItemOption.id) {
      return;
    }

    // Get existing mappings where this option is linked to option groups
    const existingMappings = await entityManager.find(MappingMenuItemOptionGroupMenuItemOption, {
      where: { menuItemOptionId: menuItemOption.id },
    });

    // If menuItemOptionGroupIds is an empty array, remove all existing mappings
    if (menuItemOptionGroups.length === 0) {
      if (existingMappings.length > 0) {
        await entityManager.delete(MappingMenuItemOptionGroupMenuItemOption, {
          menuItemOptionId: menuItemOption.id,
        });
      }
      return;
    }

    const menuItemOptionGroupIds = menuItemOptionGroups.map((group) => group.groupId);

    // Verify all option group IDs exist and belong to the same restaurant
    const existingOptionGroups = await entityManager.find(MenuItemOptionGroup, {
      where: { id: In(menuItemOptionGroupIds), restaurantId: menuItemOption.restaurantId },
      select: ['id'],
    });

    if (existingOptionGroups.length !== menuItemOptionGroupIds.length) {
      const foundIds = existingOptionGroups.map((group) => group.id);
      const missingIds = menuItemOptionGroupIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `The following option group IDs do not exist or don't belong to this restaurant: ${missingIds.join(', ')}`,
      );
    }

    // Find existing option group IDs that this option is already linked to
    const existingOptionGroupIds = existingMappings.map((mapping) => mapping.menuItemOptionGroupId);

    // Find mappings to remove (existing but not in new list)
    const mappingsToRemove = existingMappings.filter(
      (mapping) => !menuItemOptionGroupIds.includes(mapping.menuItemOptionGroupId),
    );

    // Find option group IDs to add (in new list but not existing)
    const optionGroupIdsToAdd = menuItemOptionGroupIds.filter((id) => !existingOptionGroupIds.includes(id));

    // Remove mappings that are no longer needed
    if (mappingsToRemove.length > 0) {
      const optionGroupIdsToRemove = mappingsToRemove.map((mapping) => mapping.menuItemOptionGroupId);
      await entityManager.delete(MappingMenuItemOptionGroupMenuItemOption, {
        menuItemOptionId: menuItemOption.id,
        menuItemOptionGroupId: In(optionGroupIdsToRemove),
      });
    }

    // Handle existing mappings that need price updates
    const mappingsToUpdate = existingMappings.filter((existingMapping) => {
      const matchingGroup = menuItemOptionGroups.find(
        (group) => group.groupId === existingMapping.menuItemOptionGroupId,
      );
      return matchingGroup && matchingGroup.price !== existingMapping.price;
    });

    if (mappingsToUpdate.length > 0) {
      await Promise.all(
        mappingsToUpdate.map(async (mapping) => {
          const matchingGroup = menuItemOptionGroups.find((group) => group.groupId === mapping.menuItemOptionGroupId);
          if (matchingGroup) {
            await entityManager.update(
              MappingMenuItemOptionGroupMenuItemOption,
              {
                menuItemOptionGroupId: mapping.menuItemOptionGroupId,
                menuItemOptionId: mapping.menuItemOptionId,
              },
              {
                price: matchingGroup.price,
              },
            );
          }
        }),
      );
    }

    // Add new mappings with position calculated via raw query and custom price
    if (optionGroupIdsToAdd.length > 0) {
      const values = optionGroupIdsToAdd
        .map((groupId) => {
          const matchingGroup = menuItemOptionGroups.find((group) => group.groupId === groupId);
          const priceValue = !isNil(matchingGroup?.price) ? matchingGroup.price : 'NULL';
          return `('${groupId}', '${menuItemOption.id}', COALESCE((SELECT MAX(position) FROM mapping_menu_item_option_groups_menu_item_options WHERE menu_item_option_group_id = '${groupId}'), 0) + 1, ${priceValue})`;
        })
        .join(', ');

      await entityManager.query(`
        INSERT INTO mapping_menu_item_option_groups_menu_item_options (menu_item_option_group_id, menu_item_option_id, position, price)
        VALUES ${values}
      `);
    }
  }

  async checkNameExists(
    restaurantId: string,
    internalName?: string,
    publishedName?: string,
    excludeId?: string,
  ): Promise<boolean> {
    if (internalName) {
      return NameValidationHelper.checkNameExists(
        this.menuItemOptionRepository,
        'menuItemOption',
        restaurantId,
        'internalName',
        internalName,
        excludeId,
      );
    }

    if (publishedName) {
      return NameValidationHelper.checkNameExists(
        this.menuItemOptionRepository,
        'menuItemOption',
        restaurantId,
        'publishedName',
        publishedName,
        excludeId,
      );
    }

    return false;
  }
}
