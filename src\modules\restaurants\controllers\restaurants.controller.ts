import { Pagination } from 'nestjs-typeorm-paginate';

import { UserMerchantId } from '@/common/decorators/user.decorator';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { CreateRestaurantDto } from '../dtos/create-restaurant.dto';
import { ListRestaurantDto } from '../dtos/list-restaurant.dto';
import { UpdateRestaurantDto } from '../dtos/update-restaurant.dto';
import { Restaurant } from '../entities/restaurant.entity';
import { RestaurantsService } from '../restaurants.service';

@ApiTags('Restaurants')
@Controller('restaurants')
@Roles({ userType: UserType.MERCHANT_USER, role: '*' })
export class RestaurantsController {
  constructor(private readonly restaurantsService: RestaurantsService) {}

  @Post()
  create(
    @Body() createRestaurantDto: CreateRestaurantDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Restaurant> {
    return this.restaurantsService.create(createRestaurantDto, ownerId);
  }

  @Get()
  findAll(
    @Query() listRestaurantDto: ListRestaurantDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Pagination<Restaurant>> {
    return this.restaurantsService.findAll(listRestaurantDto, ownerId);
  }

  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<Restaurant> {
    return this.restaurantsService.findOne(id, ownerId);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateRestaurantDto: UpdateRestaurantDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Restaurant> {
    return this.restaurantsService.update(id, updateRestaurantDto, ownerId);
  }

  @Put('activate/:id')
  activate(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<Restaurant> {
    return this.restaurantsService.activate(id, ownerId);
  }

  @Put('deactivate/:id')
  deactivate(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<Restaurant> {
    return this.restaurantsService.deactivate(id, ownerId);
  }
}
