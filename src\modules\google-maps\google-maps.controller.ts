import { <PERSON>, Get, Post, Body, Param, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';
import { ApiResponseDto, GeocodeResponseDto } from './dto/address-response.dto';
import { GeocodeDto, ReverseGeocodeDto } from './dto/geocode.dto';
import { Public } from '../auth/decorators/public.decorator';
import { GoogleMapsService } from './google-maps.service';

@Public()
@Controller('google-maps')
export class GoogleMapsController {
  constructor(private readonly googleMapsService: GoogleMapsService) {}

  @Post('geocode')
  // ToDo: fixed requests per minute
  @ApiOperation({ summary: 'Convert address to coordinates' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Address geocoded successfully',
    type: GeocodeResponseDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Geocoding failed' })
  async geocode(@Body() geocodeDto: GeocodeDto): Promise<ApiResponseDto<GeocodeResponseDto>> {
    const result = await this.googleMapsService.geocodeAddress(geocodeDto.address);
    return {
      success: true,
      data: result,
      message: 'Address geocoded successfully',
    };
  }

  @Post('reverse-geocode')
  @ApiOperation({ summary: 'Convert coordinates to address' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Coordinates reverse geocoded successfully',
    type: GeocodeResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Reverse geocoding failed',
  })
  async reverseGeocode(@Body() reverseGeocodeDto: ReverseGeocodeDto): Promise<ApiResponseDto<GeocodeResponseDto>> {
    const result = await this.googleMapsService.reverseGeocode(reverseGeocodeDto.latitude, reverseGeocodeDto.longitude);
    return {
      success: true,
      data: result,
      message: 'Coordinates reverse geocoded successfully',
    };
  }

  @Get('place/:placeId')
  // ToDo: set requests per minute
  @ApiOperation({ summary: 'Get place details by Place ID' })
  @ApiParam({ name: 'placeId', description: 'Google Maps Place ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Place details retrieved successfully',
    type: GeocodeResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Failed to get place details',
  })
  async getPlaceDetails(@Param('placeId') placeId: string): Promise<ApiResponseDto<GeocodeResponseDto>> {
    const result = await this.googleMapsService.getPlaceDetails(placeId);
    return {
      success: true,
      data: result,
      message: 'Place details retrieved successfully',
    };
  }
}
