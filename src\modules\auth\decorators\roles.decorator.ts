import { ABRole } from '@/modules/admins/enums/admin-role.enum';
import { applyDecorators, SetMetadata } from '@nestjs/common';
import { ApiSecurity } from '@nestjs/swagger';

import { UserType } from '../enums/user-type.enum';

export const ROLES_KEY = 'roles';

export type RoleType = any; // Define the role types for each user type
type MerchantUserRole = { userType: UserType.MERCHANT_USER; role: '*' };
type MerchantStaffRole = { userType: UserType.MERCHANT_STAFF; role: '*' };
type AdminRole = { userType: UserType.AB_ADMIN; role: ABRole[] | '*' };
type UserRole = { userType: UserType.USER; role: '*' };

export type RoleKey = MerchantUserRole | MerchantStaffRole | AdminRole | UserRole;

export function Roles(...roleKeys: RoleKey[]) {
  const roleKeysString = roleKeys
    .map((roleKey) => {
      const listRoles = typeof roleKey.role === 'string' ? [roleKey.role] : roleKey.role;
      return listRoles?.map((role) => `${roleKey.userType}:${role}`) ?? [];
    })
    .flat();
  return applyDecorators(SetMetadata(ROLES_KEY, roleKeysString), ApiSecurity('roles', roleKeysString));
}
