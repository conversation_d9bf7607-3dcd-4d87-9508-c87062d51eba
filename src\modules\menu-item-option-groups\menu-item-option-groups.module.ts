import { RestaurantsModule } from '@/modules/restaurants/restaurants.module';
import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MappingMenuItemOptionGroupMenuItemOption } from './entities/mapping-menu-item-option-group-menu-item-option.entity';
import { MenuItemOptionGroup } from './entities/menu-item-option-group.entity';
import { MenuItemOptionGroupsController } from './menu-item-option-groups.controller';
import { MenuItemOptionGroupsService } from './menu-item-option-groups.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([MenuItemOptionGroup, MappingMenuItemOptionGroupMenuItemOption]),
    RestaurantsModule,
  ],
  controllers: [MenuItemOptionGroupsController],
  providers: [MenuItemOptionGroupsService],
  exports: [MenuItemOptionGroupsService],
})
export class MenuItemOptionGroupsModule {}
