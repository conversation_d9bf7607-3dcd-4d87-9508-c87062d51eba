{"name": "anhbeo-nestjs-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"preinstall": "npx only-allow yarn", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "cross-var ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js -d src/config/typeorm.config.ts", "migration:generate": "npm run typeorm -- migration:generate src/database/migrations/$npm_config_name", "migration:run": "npm run typeorm -- migration:run && npm run seed:run", "migration:revert": "npm run typeorm -- migration:revert", "typeorm:prod": "node ./node_modules/typeorm/cli.js -d ./dist/config/typeorm.config.js", "migration:run:prod": "npm run typeorm:prod -- migration:run && npm run seed:run:prod", "seed:run": "ts-node -r tsconfig-paths/register src/database/seeders/config/run.seeders.ts", "seed:run:prod": "node ./dist/database/seeders/config/run.seeders.js", "seed:create": "ts-node -r tsconfig-paths/register src/database/seeders/config/create.seeder.ts", "prepare": "husky"}, "dependencies": {"@aws-sdk/client-s3": "^3.815.0", "@aws-sdk/s3-request-presigner": "^3.815.0", "@googlemaps/google-maps-services-js": "^3.4.1", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.1.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.1.5", "@nestjs/typeorm": "^11.0.0", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bignumber.js": "^9.3.0", "bullmq": "^5.52.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.6", "cross-var": "^1.1.0", "dotenv": "^16.5.0", "firebase-admin": "^13.3.0", "handlebars": "^4.7.8", "husky": "^9.1.7", "ioredis": "^5.6.1", "lodash": "^4.17.21", "ms": "^2.1.3", "nestjs-i18n": "^10.5.1", "nestjs-typeorm-paginate": "^4.1.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.15.6", "qs": "^6.14.0", "reflect-metadata": "^0.2.2", "resend": "^4.4.1", "rxjs": "^7.8.1", "sharp": "^0.34.2", "typeorm": "^0.3.23", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@faker-js/faker": "^9.8.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.7.7", "@swc/core": "^1.11.24", "@swc/jest": "^0.2.38", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.7", "@types/express": "^5.0.0", "@types/handlebars": "^4.0.40", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/mjml": "^4.7.4", "@types/multer": "^1.4.12", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "dayjs": "^1.11.13", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "faker": "^6.6.6", "globals": "^16.0.0", "jest": "^29.7.0", "lint-staged": "^15.5.2", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "engines": {"node": ">=20.18.1", "yarn": ">=1.22.22"}, "lint-staged": {"*.ts": ["eslint --fix"]}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": ["@swc/jest"]}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}