import { Pagination } from 'nestjs-typeorm-paginate';

import { Public } from '@/modules/auth/decorators/public.decorator';
import { Controller, Get, Param, ParseUUIDPipe, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { FilterRestaurantDto } from '../dtos/filter-restaurant.dto';
import { Restaurant } from '../entities/restaurant.entity';
import { RestaurantsService } from '../restaurants.service';

@ApiTags('(User) Restaurants')
@Controller('user/restaurants')
@Public()
export class UserRestaurantsController {
  constructor(private readonly restaurantsService: RestaurantsService) {}

  @Get()
  userFindAll(@Query() filterRestaurantDto: FilterRestaurantDto): Promise<Pagination<Restaurant>> {
    return this.restaurantsService.userFindAll(filterRestaurantDto);
  }

  @Get(':id')
  userFindOne(@Param('id', ParseUUIDPipe) id: string): Promise<Restaurant> {
    return this.restaurantsService.userFindOne(id);
  }
}
