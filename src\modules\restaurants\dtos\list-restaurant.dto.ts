import { Type } from 'class-transformer';
import { <PERSON><PERSON><PERSON>y, IsBoolean, IsN<PERSON>ber, IsOptional, IsString, IsUUI<PERSON>, <PERSON>, <PERSON> } from 'class-validator';

import { ToBoolean } from '@/common/decorators/transforms.decorator';
import { PaginationDto } from '@/common/dtos/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

export class ListRestaurantDto extends PaginationDto {
  @ApiProperty({ description: 'Filter by name', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: 'Filter by price range', required: false })
  @IsOptional()
  @IsString()
  priceRange?: string;

  @ApiProperty({ description: 'Filter by minimum star rating', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  @Type(() => Number)
  minStarRated?: number;

  @ApiProperty({ description: 'Filter by brand ID', required: false })
  @IsOptional()
  @IsUUID()
  brandId?: string;

  @ApiProperty({ description: 'Filter by restaurant tags', required: false })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  restaurantTagIds?: string[];

  @ApiProperty({ description: 'Filter by rating', required: false })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  rating?: boolean;
}
