import { Repository } from 'typeorm';

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { GoogleMapsService } from '../google-maps/google-maps.service';
import { CreateUserAddressDto } from './dto/create-user-address.dto';
import { UpdateUserAddressDto } from './dto/update-user-address.dto';
import { UserAddress } from './entities/user-address.entity';

@Injectable()
export class UserAddressesService {
  constructor(
    @InjectRepository(UserAddress)
    private userAddressesRepository: Repository<UserAddress>,
    private googleMapsService: GoogleMapsService,
  ) {}

  async findAllByUser(userId: string): Promise<UserAddress[]> {
    return this.userAddressesRepository.find({
      where: { userId, isTemporary: false },
      order: { isDefault: 'DESC', createdAt: 'DESC' },
    });
  }

  async findOneByUser(id: string, userId: string, isTemporary?: boolean): Promise<UserAddress> {
    const address = await this.userAddressesRepository.findOne({
      where: { id, userId, isTemporary: isTemporary ?? false },
    });

    if (!address) {
      throw new NotFoundException(`User address with ID ${id} not found or you don't have access to it.`);
    }

    return address;
  }

  async findOneByUserAndId(id: string, userId: string): Promise<UserAddress> {
    const address = await this.userAddressesRepository.findOne({
      where: { id, userId },
    });

    if (!address) {
      throw new NotFoundException(`User address with ID ${id} not found or you don't have access to it.`);
    }

    return address;
  }

  async create(
    createUserAddressDto: CreateUserAddressDto,
    userId: string,
    isTemporary?: boolean,
  ): Promise<UserAddress> {
    const queryBuilder = this.userAddressesRepository.createQueryBuilder('user-addresses');
    let addressComponents = {};
    try {
      const geocodeResult = await this.googleMapsService.reverseGeocode(
        createUserAddressDto.latitude,
        createUserAddressDto.longitude,
      );
      addressComponents = geocodeResult.addressComponents;
    } catch (error) {
      console.warn('Geocoding failed:', error.message);
    }

    // 2. If setting default, unset other defaults for this user
    if (createUserAddressDto.isDefault) {
      await this.userAddressesRepository
        .createQueryBuilder()
        .update(UserAddress)
        .set({ isDefault: false })
        .where('userId = :userId', { userId })
        .execute();
    }

    // 3. Insert the new address
    const insertResult = await queryBuilder
      .insert()
      .into(UserAddress)
      .values({
        ...createUserAddressDto,
        ...addressComponents,
        userId,
        ...(isTemporary ? { isTemporary: true } : {}),
      })
      .returning('*') // optional: returns the inserted row(s)
      .execute();

    return insertResult.generatedMaps[0] as UserAddress;
  }

  async update(id: string, updateUserAddressDto: UpdateUserAddressDto, userId: string): Promise<UserAddress> {
    // 1. Check if this address belong to this user
    const existingAddress = await this.findOneByUser(id, userId);

    // 2. Lat and lng new, need to call google api
    if (updateUserAddressDto.latitude !== undefined || updateUserAddressDto.longitude !== undefined) {
      const lat = updateUserAddressDto.latitude ?? existingAddress.latitude;
      const lng = updateUserAddressDto.longitude ?? existingAddress.longitude;
      try {
        const geocodeResult = await this.googleMapsService.reverseGeocode(lat, lng);
        // Assign DTO
        Object.assign(updateUserAddressDto, geocodeResult.addressComponents);
      } catch (error) {
        console.warn('Geocoding failed during update:', error.message);
      }
    }

    // 3. set other address isDefault = false if this is set isDefault
    const queryBuilder = this.userAddressesRepository.createQueryBuilder('user-addresses');
    if (updateUserAddressDto.isDefault) {
      await queryBuilder
        .update(UserAddress)
        .set({ isDefault: false })
        .where('userId = :userId', { userId })
        .andWhere('id != :id', { id })
        .execute();
    }

    // 4. Update this address
    await queryBuilder.update(UserAddress).set(updateUserAddressDto).where('id = :id', { id }).execute();

    // 5. update address
    const updatedAddress = await this.userAddressesRepository.findOne({
      where: { id },
    });

    if (!updatedAddress) {
      // not found
      throw new NotFoundException(`userAddress with ID ${id} not exist`);
    }

    return updatedAddress;
  }

  async remove(id: string, userId: string): Promise<UserAddress> {
    const address = await this.findOneByUser(id, userId);

    await this.userAddressesRepository.softDelete({ id });

    return address;
  }

  async setDefault(id: string, userId: string): Promise<UserAddress> {
    // 1. Make sure this address exists and is active for this user
    await this.findOneByUser(id, userId);

    // 2. Unset isDefault for all other active addresses of this user
    await this.userAddressesRepository.update({ userId, isTemporary: false }, { isDefault: false });

    // 3. Set this particular address as default
    await this.userAddressesRepository.update({ id }, { isDefault: true });

    // 4. Fetch and return the updated address
    return this.findOneByUser(id, userId);
  }

  async createOrUpdateTemporaryAddress(
    createUserAddressDto: CreateUserAddressDto,
    userId: string,
  ): Promise<UserAddress> {
    const existingAddress = await this.userAddressesRepository.findOne({ where: { userId, isTemporary: true } });
    if (existingAddress) {
      return this.updateTemporaryAddress(createUserAddressDto, existingAddress);
    }
    return this.create(createUserAddressDto, userId, true);
  }

  async updateTemporaryAddress(
    updateUserAddressDto: UpdateUserAddressDto,
    existingAddress: UserAddress,
  ): Promise<UserAddress> {
    // 1. Lat and lng new, need to call google api
    if (updateUserAddressDto.latitude !== undefined || updateUserAddressDto.longitude !== undefined) {
      const lat = updateUserAddressDto.latitude ?? existingAddress.latitude;
      const lng = updateUserAddressDto.longitude ?? existingAddress.longitude;
      try {
        const geocodeResult = await this.googleMapsService.reverseGeocode(lat, lng);
        // Assign DTO
        Object.assign(updateUserAddressDto, geocodeResult.addressComponents);
      } catch (error) {
        console.warn('Geocoding failed during update:', error.message);
      }
    }

    // 2. Update this address
    await this.userAddressesRepository.update(existingAddress.id, updateUserAddressDto);

    // 3. update address
    const updatedAddress = await this.userAddressesRepository.findOne({ where: { id: existingAddress.id } });
    if (!updatedAddress) {
      throw new NotFoundException(`userAddress with ID ${existingAddress.id} not exist`);
    }

    return updatedAddress;
  }

  // Temporary Address Management
  async getTemporaryAddress(userId: string) {
    const address = await this.userAddressesRepository.findOne({ where: { userId, isTemporary: true } });
    if (!address) {
      throw new NotFoundException('Temporary address not found');
    }
    return address;
  }

  async getDefaultAddress(userId: string) {
    const address = await this.userAddressesRepository
      .createQueryBuilder('address')
      .where('address.userId = :userId', { userId })
      .orderBy(
        `CASE 
          WHEN address.isDefault = true THEN 1
          WHEN address.isTemporary = false THEN 2
          ELSE 3
        END`,
      )
      .addOrderBy('address.updatedAt', 'DESC')
      .limit(1)
      .getOne();

    return address;
  }

  async hasAnyAddress(userId: string): Promise<boolean> {
    const address = await this.userAddressesRepository.findOne({ where: { userId } });
    return !!address;
  }
}
