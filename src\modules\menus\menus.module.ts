import { MenuSectionsModule } from '@/modules/menu-sections/menu-sections.module';
import { RestaurantsModule } from '@/modules/restaurants/restaurants.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MappingMenuMenuSection } from './entities/mapping-menu-menu-section.entity';
import { Menu } from './entities/menu.entity';
import { MenusController } from './menus.controller';
import { MenusService } from './menus.service';

@Module({
  imports: [TypeOrmModule.forFeature([Menu, MappingMenuMenuSection]), RestaurantsModule, MenuSectionsModule],
  controllers: [MenusController],
  providers: [MenusService],
  exports: [MenusService],
})
export class MenusModule {}
