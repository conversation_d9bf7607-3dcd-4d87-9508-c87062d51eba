import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Repository } from 'typeorm';

import { MenuItemOption } from '@/modules/menu-item-options/entities/menu-item-option.entity';
import { MenuItem } from '@/modules/menu-items/entities/menu-item.entity';
import { MenuSection } from '@/modules/menu-sections/entities/menu-section.entity';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class MenuSchedulesService {
  constructor(
    @InjectRepository(MenuSection)
    private readonly menuSectionRepository: Repository<MenuSection>,
    @InjectRepository(MenuItem)
    private readonly menuItemRepository: Repository<MenuItem>,
    @InjectRepository(MenuItemOption)
    private readonly menuItemOptionRepository: Repository<MenuItemOption>,
  ) {}

  private isProcessingMenuSection = false;
  private isProcessingMenuItem = false;
  private isProcessingMenuItemOption = false;

  private readonly logger = new Logger(MenuSchedulesService.name);

  @Cron(CronExpression.EVERY_MINUTE)
  async handleTriggerActiveMenuSection(): Promise<void> {
    if (this.isProcessingMenuSection) {
      this.logger.log('Previous menu section check is still running, skipping...');
      return;
    }

    try {
      this.isProcessingMenuSection = true;
      this.logger.log('Checking for menu sections to activate...');
      const result = await this.menuSectionRepository.update(
        {
          activeAt: IsNull(),
          scheduleActiveAt: LessThan(new Date()),
        },
        { activeAt: new Date(), scheduleActiveAt: null },
      );

      if (!result.affected) return;
      this.logger.log(`Activated ${result.affected} menu sections`);
    } catch (error) {
      this.logger.error('Error in scheduled menu section check:', error);
    } finally {
      this.isProcessingMenuSection = false;
    }
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async handleTriggerActiveMenuItem(): Promise<void> {
    if (this.isProcessingMenuItem) {
      this.logger.log('Previous menu item check is still running, skipping...');
      return;
    }

    try {
      this.isProcessingMenuItem = true;
      this.logger.log('Checking for menu items to activate...');
      const result = await this.menuItemRepository.update(
        {
          activeAt: IsNull(),
          scheduleActiveAt: LessThan(new Date()),
        },
        { activeAt: new Date(), scheduleActiveAt: null },
      );

      if (!result.affected) return;
      this.logger.log(`Activated ${result.affected} menu items`);
    } catch (error) {
      this.logger.error('Error in scheduled menu item check:', error);
    } finally {
      this.isProcessingMenuItem = false;
    }
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async handleTriggerActiveMenuItemOption(): Promise<void> {
    if (this.isProcessingMenuItemOption) {
      this.logger.log('Previous menu item option check is still running, skipping...');
      return;
    }

    try {
      this.isProcessingMenuItemOption = true;
      this.logger.log('Checking for menu item options to activate...');
      const result = await this.menuItemOptionRepository.update(
        {
          activeAt: IsNull(),
          scheduleActiveAt: LessThan(new Date()),
        },
        { activeAt: new Date(), scheduleActiveAt: null },
      );

      if (!result.affected) return;
      this.logger.log(`Activated ${result.affected} menu item options`);
    } catch (error) {
      this.logger.error('Error in scheduled menu item option check:', error);
    } finally {
      this.isProcessingMenuItemOption = false;
    }
  }
}
