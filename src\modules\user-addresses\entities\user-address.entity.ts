import { Column, Entity, Index } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';

@Entity('user-addresses')
@Index(['userId', 'isDefault'])
@Index(['userId', 'isTemporary'])
@Index(['latitude', 'longitude'])
@Index(['userId', 'isTemporary'], { unique: true, where: 'is_temporary = true AND deleted_at IS NULL' })
export class UserAddress extends BaseEntity {
  @Index()
  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Column({ type: 'decimal', precision: 10, scale: 8 })
  latitude: number;

  @Column({ type: 'decimal', precision: 11, scale: 8 })
  longitude: number;

  @Column({ name: 'full_address', type: 'text' })
  fullAddress: string;

  @Column({ name: 'street_number', nullable: true, type: 'varchar' })
  streetNumber?: string;

  @Column({ name: 'street_name', nullable: true, type: 'varchar' })
  streetName?: string;

  @Column({ nullable: true, type: 'varchar' })
  city?: string;

  @Column({ nullable: true, type: 'varchar' })
  state?: string;

  @Column({ nullable: true, type: 'varchar' })
  country?: string;

  @Column({ name: 'postal_code', nullable: true, type: 'varchar' })
  postalCode?: string;

  @Column({ name: 'address_type', type: 'varchar' })
  addressType: string;

  @Column({ name: 'address_label', type: 'varchar' })
  addressLabel: string;

  @Column({ name: 'is_default', default: false })
  isDefault: boolean;

  @Column({ name: 'is_temporary', default: false })
  isTemporary: boolean;

  @Column({ name: 'place_id', nullable: true, type: 'varchar' })
  placeId?: string | null;
}
