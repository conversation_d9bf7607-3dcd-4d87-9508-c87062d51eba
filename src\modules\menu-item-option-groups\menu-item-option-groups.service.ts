import { Pagination } from 'nestjs-typeorm-paginate';
import { DataSource, EntityManager, In, Repository } from 'typeorm';

import { PositionItemWithPriceDto } from '@/common/dtos/position-item-with-price.dto';
import { NameValidationHelper } from '@/common/helpers/name-validation.helper';
import { MenuItemOption } from '@/modules/menu-item-options/entities/menu-item-option.entity';
import { MappingMenuItemMenuItemOptionGroup } from '@/modules/menu-items/entities/mapping-menu-item-menu-item-option-group.entity';
import { MenuItem } from '@/modules/menu-items/entities/menu-item.entity';
import { RestaurantsService } from '@/modules/restaurants/restaurants.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { CreateMenuItemOptionGroupDto } from './dtos/create-menu-item-option-group.dto';
import { ListMenuItemOptionGroupDto } from './dtos/list-menu-item-option-group.dto';
import { UpdateMenuItemOptionGroupDto } from './dtos/update-menu-item-option-group.dto';
import { MappingMenuItemOptionGroupMenuItemOption } from './entities/mapping-menu-item-option-group-menu-item-option.entity';
import { MenuItemOptionGroup } from './entities/menu-item-option-group.entity';

@Injectable()
export class MenuItemOptionGroupsService {
  constructor(
    @InjectRepository(MenuItemOptionGroup)
    private menuItemOptionGroupRepository: Repository<MenuItemOptionGroup>,

    private restaurantsService: RestaurantsService,
    private dataSource: DataSource,
  ) {}

  async create(
    createMenuItemOptionGroupDto: CreateMenuItemOptionGroupDto,
    ownerId: string | null,
  ): Promise<MenuItemOptionGroup> {
    // Verify restaurant exists and user has access to it
    await this.restaurantsService.verifyAccessRestaurant(createMenuItemOptionGroupDto.restaurantId, ownerId);

    // Validate unique names using helper
    await NameValidationHelper.validateUniqueNames(
      createMenuItemOptionGroupDto.restaurantId,
      createMenuItemOptionGroupDto.internalName,
      createMenuItemOptionGroupDto.publishedName,
      undefined,
      this.checkNameExists.bind(this),
    );

    // Extract menuItemOptionIds and menuItemIds from DTO if present
    const { menuItemOptionIds, menuItemIds, ...menuItemOptionGroupData } = createMenuItemOptionGroupDto;

    let id: string = '';
    await this.dataSource.transaction(async (entityManager) => {
      // Create menu item option group
      const menuItemOptionGroup = entityManager.create(MenuItemOptionGroup, menuItemOptionGroupData);

      // Save the menu item option group entity
      const savedMenuItemOptionGroup = await entityManager.save(menuItemOptionGroup);

      // Handle menu item option relationships
      await this.handleMenuItemOptionRelationship(savedMenuItemOptionGroup, menuItemOptionIds, entityManager);

      // Handle menu item relationships
      await this.handleMenuItemRelationship(savedMenuItemOptionGroup, menuItemIds, entityManager);

      id = savedMenuItemOptionGroup.id;
    });

    return this.findOne(id, ownerId);
  }

  async findAll(
    listMenuItemOptionGroupDto: ListMenuItemOptionGroupDto,
    ownerId: string | null,
  ): Promise<Pagination<MenuItemOptionGroup>> {
    const { internalName, publishedName, rule, restaurantId, page, limit } = listMenuItemOptionGroupDto;

    const queryBuilder = this.menuItemOptionGroupRepository.createQueryBuilder('menuItemOptionGroup');

    // Add counts as subqueries
    queryBuilder
      .addSelect(
        (subQuery) =>
          subQuery
            .select('COUNT(*)')
            .from(MappingMenuItemOptionGroupMenuItemOption, 'mapping_options')
            .where('mapping_options.menu_item_option_group_id = menuItemOptionGroup.id'),
        'optionCount',
      )
      .addSelect(
        (subQuery) =>
          subQuery
            .select('COUNT(*)')
            .from(MappingMenuItemMenuItemOptionGroup, 'mapping_usage')
            .where('mapping_usage.menu_item_option_group_id = menuItemOptionGroup.id'),
        'itemCount',
      );

    if (restaurantId) {
      queryBuilder.andWhere('menuItemOptionGroup.restaurantId = :restaurantId', { restaurantId });
    }

    // For merchant users, only show menu item option groups they have access to
    if (ownerId) {
      queryBuilder
        .leftJoin('menuItemOptionGroup.restaurant', 'restaurant')
        .leftJoin('restaurant.brand', 'brand')
        .leftJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    // Apply filters
    if (rule) {
      queryBuilder.andWhere('menuItemOptionGroup.rule = :rule', { rule });
    }

    if (internalName) {
      queryBuilder.andWhere('menuItemOptionGroup.internalName ILIKE :internalName', {
        internalName: `%${internalName}%`,
      });
    }

    if (publishedName) {
      queryBuilder.andWhere('menuItemOptionGroup.publishedName ILIKE :publishedName', {
        publishedName: `%${publishedName}%`,
      });
    }

    // Order by most recently updated
    queryBuilder.orderBy('menuItemOptionGroup.updatedAt', 'DESC');

    // Use getRawAndEntities to preserve addSelect fields
    const totalCount = await queryBuilder.getCount();
    const { entities, raw } = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getRawAndEntities();

    // Map the raw data to entities to include addSelect fields
    const itemsWithCounts = entities.map((entity, index) => {
      const rawItem = raw[index];
      entity.optionCount = parseInt(rawItem.optionCount || '0', 10);
      entity.itemCount = parseInt(rawItem.itemCount || '0', 10);
      return entity;
    });

    return {
      items: itemsWithCounts,
      meta: {
        totalItems: totalCount,
        itemCount: itemsWithCounts.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
      },
    };
  }

  async findOne(id: string, ownerId: string | null): Promise<MenuItemOptionGroup> {
    const queryBuilder = this.menuItemOptionGroupRepository
      .createQueryBuilder('menuItemOptionGroup')
      .where('menuItemOptionGroup.id = :id', { id })
      .leftJoinAndSelect('menuItemOptionGroup.restaurant', 'restaurant')
      .leftJoinAndSelect('menuItemOptionGroup.mappingMenuItemOptions', 'mappingMenuItemOptions')
      .leftJoinAndSelect('mappingMenuItemOptions.menuItemOption', 'menuItemOption')
      .leftJoinAndSelect('menuItemOptionGroup.mappingMenuItemMenus', 'mappingMenuItemMenus')
      .leftJoinAndSelect('mappingMenuItemMenus.menuItem', 'menuItem');

    // For merchant users, only allow access to menu item option groups they have access to
    if (ownerId) {
      queryBuilder
        .leftJoin('restaurant.brand', 'brand')
        .leftJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    const menuItemOptionGroup = await queryBuilder.getOne();

    if (!menuItemOptionGroup) {
      throw new NotFoundException(`Menu item option group with ID ${id} not found or you don't have access to it`);
    }

    // Calculate counts directly from loaded relations
    menuItemOptionGroup.optionCount = menuItemOptionGroup.mappingMenuItemOptions?.length ?? 0;

    menuItemOptionGroup.itemCount = menuItemOptionGroup.mappingMenuItemMenus?.length ?? 0;

    return menuItemOptionGroup;
  }

  async update(
    id: string,
    updateMenuItemOptionGroupDto: UpdateMenuItemOptionGroupDto,
    ownerId: string | null,
  ): Promise<MenuItemOptionGroup> {
    const menuItemOptionGroup = await this.findOne(id, ownerId);

    // Validate unique names using helper
    await NameValidationHelper.validateUniqueNames(
      menuItemOptionGroup.restaurantId,
      updateMenuItemOptionGroupDto.internalName,
      updateMenuItemOptionGroupDto.publishedName,
      id,
      this.checkNameExists.bind(this),
    );

    // Extract menuItemOptionIds and menuItemIds from DTO if present
    const { menuItemOptionIds, menuItemIds, ...menuItemOptionGroupData } = updateMenuItemOptionGroupDto;

    // Update basic properties
    Object.assign(menuItemOptionGroup, menuItemOptionGroupData);
    delete menuItemOptionGroup.mappingMenuItemOptions;
    await this.dataSource.transaction(async (entityManager) => {
      // Handle menu item option relationships
      await this.handleMenuItemOptionRelationship(menuItemOptionGroup, menuItemOptionIds, entityManager);

      // Handle menu item relationships
      await this.handleMenuItemRelationship(menuItemOptionGroup, menuItemIds, entityManager);

      // Save the updated entity
      await entityManager.save(menuItemOptionGroup);
    });

    return this.findOne(id, ownerId);
  }

  /**
   * Helper method to handle menu item option relationships for menu item option groups
   * @param menuItemOptionGroup The menu item option group entity to update
   * @param menuItemOptionIds Array of menu item option IDs to link, or undefined to skip updating
   */
  private async handleMenuItemOptionRelationship(
    menuItemOptionGroup: MenuItemOptionGroup,
    menuItemOptionIds: PositionItemWithPriceDto[] | undefined,
    entityManager: EntityManager,
  ): Promise<void> {
    if (menuItemOptionIds === undefined) {
      return;
    }

    // Clear existing mapping relationships
    if (menuItemOptionGroup.id) {
      await entityManager.delete(MappingMenuItemOptionGroupMenuItemOption, {
        menuItemOptionGroupId: menuItemOptionGroup.id,
      });
    }

    // If menuItemOptionIds is an empty array, we're done (all relationships cleared)
    if (menuItemOptionIds.length === 0) {
      return;
    }

    // Extract IDs from PositionItemWithPriceDto array
    const ids = menuItemOptionIds.map((item) => item.id);

    // Find menu item options that match both the provided IDs and the restaurant ID
    const menuItemOptions = await entityManager.find(MenuItemOption, {
      where: { id: In(ids), restaurantId: menuItemOptionGroup.restaurantId },
      select: ['id'],
    });

    // Check if all menu item option IDs exist and belong to the restaurant
    if (menuItemOptions.length !== ids.length) {
      const foundIds = menuItemOptions.map((menuItemOption) => menuItemOption.id);
      const missingIds = ids.filter((id) => !foundIds.includes(id));

      // If there are IDs that don't exist at all
      if (missingIds.length > 0) {
        throw new NotFoundException(`The following menu item option IDs do not exist: ${missingIds.join(', ')}`);
      }
    }

    // Create mapping relationships with positions and price
    if (menuItemOptionGroup.id) {
      const mappingData = menuItemOptionIds.map((item) =>
        entityManager.create(MappingMenuItemOptionGroupMenuItemOption, {
          menuItemOptionGroupId: menuItemOptionGroup.id,
          menuItemOptionId: item.id,
          position: item.position,
          price: item.price,
        }),
      );
      await entityManager.save(mappingData);
    }
  }

  async getListByRestaurantId(restaurantId: string, menuItemOptionGroupIds: string[]) {
    return this.menuItemOptionGroupRepository.find({
      where: { id: In(menuItemOptionGroupIds), restaurantId },
    });
  }

  /**
   * Helper method to handle menu item relationships for option groups (add option group to menu items)
   * @param menuItemOptionGroup The option group entity to update
   * @param menuItemIds Array of menu item IDs to link, or undefined to skip updating
   */
  private async handleMenuItemRelationship(
    menuItemOptionGroup: MenuItemOptionGroup,
    menuItemIds: string[] | undefined,
    entityManager: EntityManager,
  ): Promise<void> {
    // If menuItemIds is undefined, don't update menu items
    if (menuItemIds === undefined) {
      return;
    }

    if (!menuItemOptionGroup.id) {
      return;
    }

    // Get existing mappings where this option group is linked to menu items
    const existingMappings = await entityManager.find(MappingMenuItemMenuItemOptionGroup, {
      where: { menuItemOptionGroupId: menuItemOptionGroup.id },
      select: ['menuItemId', 'menuItemOptionGroupId'],
    });

    // If menuItemIds is an empty array, remove all existing mappings
    if (menuItemIds.length === 0) {
      if (existingMappings.length > 0) {
        await entityManager.delete(MappingMenuItemMenuItemOptionGroup, {
          menuItemOptionGroupId: menuItemOptionGroup.id,
        });
      }
      return;
    }

    // Verify all menu item IDs exist and belong to the same restaurant
    const existingMenuItems = await entityManager.find(MenuItem, {
      where: { id: In(menuItemIds), restaurantId: menuItemOptionGroup.restaurantId },
      select: ['id'],
    });

    if (existingMenuItems.length !== menuItemIds.length) {
      const foundIds = existingMenuItems.map((menuItem) => menuItem.id);
      const missingIds = menuItemIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `The following menu item IDs do not exist or don't belong to this restaurant: ${missingIds.join(', ')}`,
      );
    }

    // Find existing menu item IDs that this option group is already linked to
    const existingMenuItemIds = existingMappings.map((mapping) => mapping.menuItemId);

    // Find mappings to remove (existing but not in new list)
    const mappingsToRemove = existingMappings.filter((mapping) => !menuItemIds.includes(mapping.menuItemId));

    // Find menu item IDs to add (in new list but not existing)
    const menuItemIdsToAdd = menuItemIds.filter((id) => !existingMenuItemIds.includes(id));

    // Remove mappings that are no longer needed
    if (mappingsToRemove.length > 0) {
      const menuItemIdsToRemove = mappingsToRemove.map((mapping) => mapping.menuItemId);
      await entityManager.delete(MappingMenuItemMenuItemOptionGroup, {
        menuItemOptionGroupId: menuItemOptionGroup.id,
        menuItemId: In(menuItemIdsToRemove),
      });
    }

    // Add new mappings with position calculated via raw query
    if (menuItemIdsToAdd.length > 0) {
      const values = menuItemIdsToAdd
        .map(
          (menuItemId) =>
            `('${menuItemId}', '${menuItemOptionGroup.id}', COALESCE((SELECT MAX(position) FROM mapping_menu_items_menu_item_option_groups WHERE menu_item_id = '${menuItemId}'), 0) + 1)`,
        )
        .join(', ');

      await entityManager.query(`
        INSERT INTO mapping_menu_items_menu_item_option_groups (menu_item_id, menu_item_option_group_id, position)
        VALUES ${values}
      `);
    }
  }

  async checkNameExists(
    restaurantId: string,
    internalName?: string,
    publishedName?: string,
    excludeId?: string,
  ): Promise<boolean> {
    if (internalName) {
      return NameValidationHelper.checkNameExists(
        this.menuItemOptionGroupRepository,
        'menuItemOptionGroup',
        restaurantId,
        'internalName',
        internalName,
        excludeId,
      );
    }

    if (publishedName) {
      return NameValidationHelper.checkNameExists(
        this.menuItemOptionGroupRepository,
        'menuItemOptionGroup',
        restaurantId,
        'publishedName',
        publishedName,
        excludeId,
      );
    }

    return false;
  }

  async delete(id: string, ownerId: string | null): Promise<MenuItemOptionGroup> {
    const menuItemOptionGroup = await this.findOne(id, ownerId);

    await this.dataSource.transaction(async (manager) => {
      // Soft delete all mapping relations first
      await manager.delete(MappingMenuItemOptionGroupMenuItemOption, {
        menuItemOptionGroupId: menuItemOptionGroup.id,
      });
      await manager.delete(MappingMenuItemMenuItemOptionGroup, { menuItemOptionGroupId: menuItemOptionGroup.id });

      // Then soft delete the menu item option group
      await manager.softDelete(MenuItemOptionGroup, { id: menuItemOptionGroup.id });
    });

    return menuItemOptionGroup;
  }
}
