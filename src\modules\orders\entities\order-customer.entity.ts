import { Column, Entity, OneToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';

import { Order } from './order.entity';

@Entity('order_customers')
export class OrderCustomer extends BaseEntity {
  @Column({ name: 'name', type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  phone: string | null;

  @Column({ type: 'varchar', nullable: true })
  phoneCountryCode: string | null;

  @Column({ name: 'email', nullable: true, type: 'varchar' })
  email?: string | null;

  @Column({ type: 'decimal', precision: 10, scale: 8 })
  latitude: number;

  @Column({ type: 'decimal', precision: 11, scale: 8 })
  longitude: number;

  @Column({ name: 'full_address', type: 'text' })
  fullAddress: string;

  @Column({ name: 'street_number', nullable: true, type: 'varchar' })
  streetNumber?: string;

  @Column({ name: 'street_name', nullable: true, type: 'varchar' })
  streetName?: string;

  @Column({ nullable: true, type: 'varchar' })
  city?: string;

  @Column({ nullable: true, type: 'varchar' })
  state?: string;

  @Column({ nullable: true, type: 'varchar' })
  country?: string;

  @Column({ name: 'postal_code', nullable: true, type: 'varchar' })
  postalCode?: string;

  @Column({ name: 'address_type', type: 'varchar' })
  addressType: string;

  @Column({ name: 'address_label', type: 'varchar' })
  addressLabel: string;

  @Column({ name: 'place_id', nullable: true, type: 'varchar' })
  placeId?: string | null;

  @OneToOne(() => Order, (order) => order.orderCustomer)
  order?: WrapperType<Order>;
}
