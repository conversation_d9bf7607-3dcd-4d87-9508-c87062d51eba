import { IsNotEmpty, <PERSON><PERSON>ptional, IsString, <PERSON>U<PERSON><PERSON>, ValidateIf } from 'class-validator';

import { OnlyOneField } from '@/common/validators/single-field.validator';
import { ApiProperty } from '@nestjs/swagger';

export class CheckNameExistsDto {
  @ApiProperty({ description: 'ID of the restaurant' })
  @IsUUID()
  restaurantId: string;

  @ApiProperty({ description: 'Internal name of the entity', required: false })
  @ValidateIf((o) => o.internalName !== undefined || o.publishedName === undefined)
  @OnlyOneField()
  @IsString()
  @IsNotEmpty()
  internalName?: string;

  @ApiProperty({ description: 'Published name of the entity', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  publishedName?: string;

  @ApiProperty({ description: 'ID of the entity to exclude' })
  @IsOptional()
  @IsUUID()
  excludeId?: string;
}

export class NameExistsResponseDto {
  exists: boolean;
}
