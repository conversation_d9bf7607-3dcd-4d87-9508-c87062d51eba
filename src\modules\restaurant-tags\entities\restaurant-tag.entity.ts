import { Column, Entity, ManyToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';

@Entity('restaurant_tags')
export class RestaurantTag extends BaseEntity {
  @Column({ name: 'name', type: 'varchar' })
  name: string;

  @ManyToMany(() => Restaurant, (restaurant) => restaurant.tags)
  restaurants: Restaurant[];
}
