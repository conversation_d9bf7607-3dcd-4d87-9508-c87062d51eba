import { Pagination } from 'nestjs-typeorm-paginate';
import { DataSource, EntityManager, Repository } from 'typeorm';

import { PositionItemDto } from '@/common/dtos/position-item.dto';
import { NameValidationHelper } from '@/common/helpers/name-validation.helper';
import { MenuSectionsService } from '@/modules/menu-sections/menu-sections.service';
import { RestaurantsService } from '@/modules/restaurants/restaurants.service';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { MappingMenuSectionMenuItem } from '../menu-sections/entities/mapping-menu-section-menu-item.entity';
import { CreateMenuDto } from './dtos/create-menu.dto';
import { DuplicateMenuDto } from './dtos/duplicate-menu.dto';
import { ListMenuDto, MenuSortBy } from './dtos/list-menu.dto';
import { UpdateMenuDto } from './dtos/update-menu.dto';
import { MappingMenuMenuSection } from './entities/mapping-menu-menu-section.entity';
import { Menu } from './entities/menu.entity';

@Injectable()
export class MenusService {
  constructor(
    @InjectRepository(Menu)
    private menuRepository: Repository<Menu>,

    private menuSectionsSerivce: MenuSectionsService,
    private restaurantsService: RestaurantsService,
    private dataSource: DataSource,
  ) {}

  async create(createMenuDto: CreateMenuDto, ownerId: string | null): Promise<Menu> {
    // Verify restaurant exists and user has access to it
    const restaurant = await this.restaurantsService.verifyAccessRestaurant(createMenuDto.restaurantId, ownerId);

    // Check if name already exists
    const nameExists = await this.checkNameExists(createMenuDto.restaurantId, createMenuDto.name);
    if (nameExists) {
      throw new BadRequestException('Menu name already exists');
    }

    // Extract menuSectionIds from DTO if present
    const { menuSectionIds, ...menuData } = createMenuDto;

    let id: string = '';
    await this.dataSource.transaction(async (entityManager) => {
      // Create menu
      const menu = entityManager.create(Menu, menuData);

      // Save the menu
      const savedMenu = await entityManager.save(menu);

      // Handle menu section relationships
      await this.handleMenuSectionRelationship(savedMenu, menuSectionIds, restaurant.id, entityManager);

      id = savedMenu.id;
    });

    return this.findOne(id, ownerId);
  }

  async findAll(listMenuDto: ListMenuDto, ownerId: string | null): Promise<Pagination<Menu>> {
    const { name, restaurantId, page, limit, sortBy, sort } = listMenuDto;

    const queryBuilder = this.menuRepository.createQueryBuilder('menu');

    if (restaurantId) {
      queryBuilder.andWhere('menu.restaurantId = :restaurantId', { restaurantId });
    }

    // For merchant users, only show menus they have access to
    if (ownerId) {
      queryBuilder
        .leftJoin('menu.restaurant', 'restaurant')
        .leftJoin('restaurant.brand', 'brand')
        .leftJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    // Apply filters
    if (name) {
      queryBuilder.andWhere('menu.name ILIKE :name', { name: `%${name}%` });
    }

    // Add count of sections as subquery
    queryBuilder.addSelect(
      (subQuery) =>
        subQuery
          .select('COUNT(*)')
          .from(MappingMenuMenuSection, 'mappingMenuSection')
          .where('mappingMenuSection.menuId = menu.id'),
      'sectionsCount',
    );

    // Add count of items as subquery
    queryBuilder.addSelect(
      (subQuery) =>
        subQuery
          .select('COUNT(*)')
          .from(MappingMenuMenuSection, 'mappingMenuSection')
          .leftJoin(
            MappingMenuSectionMenuItem,
            'mappingMenuItem',
            'mappingMenuItem.menuSectionId = mappingMenuSection.menuSectionId',
          )
          .where('mappingMenuSection.menuId = menu.id'),
      'itemsCount',
    );

    // Apply sorting
    const sortField = this.getSortField(sortBy);
    queryBuilder.orderBy(sortField, sort);

    // Use getRawAndEntities to preserve addSelect fields
    const totalCount = await queryBuilder.getCount();
    const { entities, raw } = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getRawAndEntities();

    // Map the raw data to entities to include addSelect fields
    const itemsWithCounts = entities.map((entity, index) => {
      const rawItem = raw[index];
      entity.sectionsCount = parseInt(rawItem.sectionsCount || '0', 10);
      entity.itemsCount = parseInt(rawItem.itemsCount || '0', 10);
      return entity;
    });

    return {
      items: itemsWithCounts,
      meta: {
        totalItems: totalCount,
        itemCount: itemsWithCounts.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Get the SQL field name for sorting
   * @param sortBy The sort field from DTO
   */
  private getSortField(sortBy: MenuSortBy): string {
    switch (sortBy) {
      case MenuSortBy.NAME:
        return 'menu.name';
      case MenuSortBy.UPDATED_AT:
        return 'menu.updatedAt';
      case MenuSortBy.ACTIVE_AT:
        return 'menu.activeAt';
      case MenuSortBy.SECTIONS_COUNT:
        return '"sectionsCount"';
      case MenuSortBy.ITEMS_COUNT:
        return '"itemsCount"';
      default:
        return 'menu.updatedAt';
    }
  }

  async findOne(id: string, ownerId: string | null): Promise<Menu> {
    const queryBuilder = this.menuRepository
      .createQueryBuilder('menu')
      .where('menu.id = :id', { id })
      .leftJoinAndSelect('menu.mappingMenuSections', 'mappingMenuSections')
      .leftJoinAndSelect('mappingMenuSections.menuSection', 'menuSection')
      .leftJoinAndSelect('menuSection.availableSchedule', 'availableSchedule')
      .leftJoinAndSelect('menuSection.mappingMenuItems', 'mappingMenuItems')
      .leftJoinAndSelect('mappingMenuItems.menuItem', 'menuItem')
      .leftJoinAndSelect('menu.restaurant', 'restaurant');

    // For merchant users, only allow access to menus they have access to
    if (ownerId) {
      queryBuilder
        .leftJoin('restaurant.brand', 'brand')
        .leftJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    const menu = await queryBuilder.getOne();

    if (!menu) {
      throw new NotFoundException(`Menu with ID ${id} not found or you don't have access to it`);
    }

    menu.sectionsCount = menu.menuSections?.length ?? 0;

    for (const section of menu.menuSections ?? []) {
      section.itemsCount = section.menuItems?.length ?? 0;
      section.itemOutOfStockCount = section.menuItems?.filter((m) => m.activeAt === null).length ?? 0;
    }
    menu.itemsCount = menu.menuSections?.reduce((acc, section) => acc + (section.itemsCount ?? 0), 0) ?? 0;
    menu.itemOutOfStockCount =
      menu.menuSections?.reduce((acc, section) => acc + (section.itemOutOfStockCount ?? 0), 0) ?? 0;

    return menu;
  }

  async update(id: string, updateMenuDto: UpdateMenuDto, ownerId: string | null): Promise<Menu> {
    const menu = await this.findOne(id, ownerId);

    // Check if name already exists (excluding current item)
    if (updateMenuDto.name) {
      const nameExists = await this.checkNameExists(menu.restaurantId, updateMenuDto.name, id);
      if (nameExists) {
        throw new BadRequestException('Menu name already exists');
      }
    }

    // Extract menuSectionIds from DTO if present
    const { menuSectionIds, ...menuData } = updateMenuDto;

    // Update basic properties
    Object.assign(menu, menuData);
    delete menu.mappingMenuSections;
    await this.dataSource.transaction(async (entityManager) => {
      // Handle menu section relationships if menuSectionIds are provided
      await this.handleMenuSectionRelationship(menu, menuSectionIds, undefined, entityManager);

      // Save the updated entity
      await entityManager.save(menu);
    });
    return this.findOne(id, ownerId);
  }

  async delete(id: string, ownerId: string | null) {
    const menu = await this.findOne(id, ownerId);
    if (menu.activeAt) {
      throw new BadRequestException('Cannot delete an active menu');
    }

    await this.dataSource.transaction(async (manager) => {
      // Soft delete mapping relations first
      await manager.delete(MappingMenuMenuSection, { menuId: menu.id });

      // Then soft delete the menu
      await manager.softDelete(Menu, { id: menu.id });
    });

    return menu;
  }

  async duplicate(duplicateMenuDto: DuplicateMenuDto, ownerId: string | null) {
    const menu = await this.findOne(duplicateMenuDto.menuId, ownerId);
    const newMenu = await this.create({ ...menu, name: duplicateMenuDto.name }, ownerId);
    return newMenu;
  }

  async activate(id: string, ownerId: string | null): Promise<Menu> {
    const menu = await this.findOne(id, ownerId);

    try {
      return await this.dataSource.transaction(async (manager) => {
        // Deactivate all other menus
        await manager
          .createQueryBuilder()
          .update(Menu)
          .set({ activeAt: null })
          .where('restaurantId = :restaurantId', { restaurantId: menu.restaurantId })
          .andWhere('id != :menuId', { menuId: id })
          .execute();

        // Activate current menu
        menu.activeAt = new Date();
        return manager.save(menu);
      });
    } catch (error) {
      throw new Error(`Failed to activate menu: ${error.message}`);
    }
  }

  async deactivate(id: string, ownerId: string | null): Promise<Menu> {
    const menu = await this.findOne(id, ownerId);

    menu.activeAt = null;

    return this.menuRepository.save(menu);
  }

  /**
   * Helper method to handle menu section relationships for menus
   * @param menu The menu entity to update
   * @param menuSectionIds Array of menu section IDs to link, or undefined to skip updating
   */
  private async handleMenuSectionRelationship(
    menu: Menu,
    menuSectionIds: PositionItemDto[] | undefined,
    restaurantId: string | undefined,
    entityManager: EntityManager,
  ): Promise<void> {
    // If menuSectionIds is undefined, don't update menuSections
    if (menuSectionIds === undefined) {
      return;
    }

    // Clear existing mapping relationships
    if (menu.id) {
      await entityManager.delete(MappingMenuMenuSection, { menuId: menu.id });
    }

    // If menuSectionIds is an empty array, we're done (all relationships cleared)
    if (menuSectionIds.length === 0) {
      return;
    }

    // Extract IDs from PositionItemDto array
    const ids = menuSectionIds.map((item) => (typeof item === 'string' ? item : item.id));

    const menuRestaurantId = menu.restaurant?.id ?? restaurantId;
    if (!menuRestaurantId) {
      throw new NotFoundException('brandId not exist');
    }
    // Find menu sections that match both the provided IDs and the brand ID
    const menuSections = await this.menuSectionsSerivce.getListByRestaurantId(menuRestaurantId, ids);

    // Check if all menu section IDs exist and belong to the brand
    if (menuSections.length !== ids.length) {
      const foundIds = menuSections.map((menuSection) => menuSection.id);
      const missingIds = ids.filter((id) => !foundIds.includes(id));

      // If there are IDs that don't exist at all
      if (missingIds.length > 0) {
        throw new NotFoundException(`The following menu section IDs do not exist: ${missingIds.join(', ')}`);
      }
    }

    // Create mapping relationships with positions
    if (menu.id) {
      const mappingData = menuSectionIds.map((item) =>
        entityManager.create(MappingMenuMenuSection, {
          menuId: menu.id,
          menuSectionId: item.id,
          position: item.position,
        }),
      );
      await entityManager.save(mappingData);
    }
  }

  async checkNameExists(restaurantId: string, name: string, excludeId?: string): Promise<boolean> {
    return NameValidationHelper.checkNameExists(this.menuRepository, 'menu', restaurantId, 'name', name, excludeId);
  }
}
