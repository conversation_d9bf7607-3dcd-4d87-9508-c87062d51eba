import { IngredientsModule } from '@/modules/ingredients/ingredients.module';
import { RestaurantsModule } from '@/modules/restaurants/restaurants.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MenuItemsController } from './controllers/menu-items.controller';
import { StaffMenuItemsController } from './controllers/staff-menu-items.controller';
import { UserMenuItemsController } from './controllers/user-menu-items.controller';
import { MappingMenuItemMenuItemOptionGroup } from './entities/mapping-menu-item-menu-item-option-group.entity';
import { MenuItem } from './entities/menu-item.entity';
import { MenuItemsService } from './menu-items.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([MenuItem, MappingMenuItemMenuItemOptionGroup]),
    IngredientsModule,
    RestaurantsModule,
  ],
  controllers: [MenuItemsController, UserMenuItemsController, StaffMenuItemsController],
  providers: [MenuItemsService],
  exports: [MenuItemsService],
})
export class MenuItemsModule {}
