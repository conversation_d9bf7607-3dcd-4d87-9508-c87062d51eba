import { Type } from 'class-transformer';
import { <PERSON>NotEmpty, <PERSON><PERSON><PERSON>ber, IsOptional, IsString, Max, Min } from 'class-validator';

import { ToBoolean } from '@/common/decorators/transforms.decorator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUserAddressDto {
  @ApiProperty({
    description: 'Latitude coordinate',
    example: 40.7128,
    minimum: -90,
    maximum: 90,
  })
  @Type(() => Number)
  @IsNumber()
  @Min(-90)
  @Max(90)
  latitude: number;

  @ApiProperty({
    description: 'Longitude coordinate',
    example: -74.006,
    minimum: -180,
    maximum: 180,
  })
  @Type(() => Number)
  @IsNumber()
  @Min(-180)
  @Max(180)
  longitude: number;

  @ApiProperty({
    description: 'Complete formatted address',
    example: '123 Main St, New York, NY 10001, USA',
  })
  @IsString()
  @IsNotEmpty()
  fullAddress: string;

  @ApiProperty({ description: 'Type of address' })
  @IsString()
  addressType: string;

  @ApiProperty({
    description: 'User-friendly label for the address',
    example: 'Home',
  })
  @IsString()
  @IsNotEmpty()
  addressLabel: string;

  @ApiProperty({
    description: 'Whether this is the default address',
    example: false,
    required: false,
  })
  @ToBoolean()
  @IsOptional()
  isDefault?: boolean;

  @ApiProperty({
    description: 'Google Maps Place ID',
    required: false,
  })
  @IsString()
  @IsOptional()
  placeId?: string;
}
