const axios = require('axios');
const { faker, fakerEN } = require('@faker-js/faker');
// Create an axios instance with cookie jar support
const api = axios.create({
  baseURL: 'http://localhost:3002',
  withCredentials: true, // Important for sending cookies with requests
});

api.interceptors.response.use((response) => {
  console.log('url:', response.request.path, 'method:', response.request.method);
  return response;
});

const post = async (url, body) => {
  const response = await api.post(url, body);
  return response.data;
};

const get = async (url, params) => {
  const response = await api.get(url, { params });
  return response.data;
};

const deleteRequest = async (url) => {
  const response = await api.delete(url);
  return response.data;
};

const put = async (url, body, params) => {
  const response = await api.put(url, body, { params });
  return response.data;
};

const MERCHANT_USERS = '/merchant-users';
const RESTAURANT_TAGS = '/restaurant-tags';
const MERCHANT_ACCOUNTS = '/merchant-accounts';
const RESTAURENTS = '/restaurants';
const BRANDS = '/brands';
const MENUS = '/menus';
const MENU_SECTIONS = '/menu-sections';
const MENU_ITEMS = '/menu-items';
const MENU_ITEM_OPTION_GROUPS = '/menu-item-option-groups';
const MENU_ITEM_OPTIONS = '/menu-item-options';
const INGREDIENTS = '/ingredients';
const MENU_ITEMS_AND_OPTIONS = '/menu-items-and-options';
const CART_ITEMS = '/cart-items';
const ORDERS = '/orders';

const paginationParams = { page: 1, limit: 10 };

const commonTest = async ({
  path,
  itemId = undefined,
  bodyUpdate,
  bodyCreate,
  activate = true,
  ban = false,
  params,
  paramsDetail = {},
  paramsUpdate = {},
}) => {
  let itemCreated;
  if (bodyCreate) {
    itemCreated = await post(path, bodyCreate);
    itemId = itemCreated.id;
  }
  await get(path, params ?? paginationParams);
  await get(`${path}/${itemId}`, paramsDetail);
  await put(`${path}/${itemId}`, bodyUpdate, paramsUpdate);
  if (activate) {
    await put(`${path}/deactivate/${itemId}`);
    await put(`${path}/activate/${itemId}`);
  }
  if (ban) {
    await put(`${path}/ban/${itemId}`);
    await put(`${path}/unban/${itemId}`);
  }

  return itemCreated;
};

const runTestAllCase = async (merchantAccount, tag) => {
  const restaurantBody = {
    name: `Restaurent ${fakerEN.company.name()}`,
    avatarImg:
      'https://anhbeodev.s3.ap-southeast-1.amazonaws.com/restaurant-avatar/*************-3c6bfaff-f44f-4cb5-b4ac-2acf249ccb44.png',
    backgroundImg:
      'https://anhbeodev.s3.ap-southeast-1.amazonaws.com/restaurant-banner/*************-4bc39265-e5d7-441e-bfd7-a7bfd55f6d6d.jpeg',
    priceRange: 'string',
  };

  const brandBody = { name: `Brand ${fakerEN.company.name()}`, merchantAccountId: merchantAccount.id };
  const brandUpdateBody = { name: `Brand ${fakerEN.company.name()}` };
  const brand = await commonTest({ path: BRANDS, bodyCreate: brandBody, bodyUpdate: brandUpdateBody });

  const resCrBody = { ...restaurantBody, brandId: brand.id, tagIds: [tag.id] };
  const restaurant = await commonTest({ path: RESTAURENTS, bodyCreate: resCrBody, bodyUpdate: restaurantBody });

  // --------------- Create and test Ingredient ----------------------
  console.log('Testing Ingredients...');
  const ingCrBody = {
    internalName: `Ingredient ${fakerEN.company.name()}`,
    publishedName: `Published Ingredient ${fakerEN.company.name()}`,
    restaurantId: restaurant.id,
  };

  const ingUdBody = {
    internalName: `Updated Ingredient ${fakerEN.company.name()}`,
    publishedName: `Updated Published Ingredient ${fakerEN.company.name()}`,
  };

  const ingredients = await commonTest({
    path: INGREDIENTS,
    bodyCreate: ingCrBody,
    bodyUpdate: ingUdBody,
    activate: false,
  });

  // --------------- Create and test Menu Item Option Group 1 ---------------

  console.log('Testing Menu Item Option Groups 1...');
  const grCrBody = {
    internalName: `Option Group ${fakerEN.company.name()}`,
    publishedName: `Published Option Group ${fakerEN.company.name()}`,
    rule: 'option',
    type: 'item_customization',
    restaurantId: restaurant.id,
  };

  const grUdBody = {
    internalName: `Updated Option Group ${fakerEN.company.name()}`,
    publishedName: `Updated Published Option Group ${fakerEN.company.name()}`,
    rule: 'option_max',
    type: 'item_customization',
    maxAmount: 3,
  };

  const groups = await commonTest({
    path: MENU_ITEM_OPTION_GROUPS,
    bodyCreate: grCrBody,
    bodyUpdate: grUdBody,
    activate: false,
  });

  // --------------- Create and test Menu Section 1 ---------------
  console.log('Testing Menu Sections 1...');

  const availableSchedule = [
    { day: 0, start: '00:00', end: '23:59' },
    { day: 1, start: '00:00', end: '23:59' },
    { day: 2, start: '00:00', end: '23:59' },
    { day: 3, start: '00:00', end: '23:59' },
    { day: 4, start: '00:00', end: '23:59' },
    { day: 5, start: '00:00', end: '23:59' },
    { day: 6, start: '00:00', end: '23:59' },
  ];

  const secCrBody = {
    internalName: `Menu Section ${fakerEN.company.name()}`,
    publishedName: `Published Menu Section ${fakerEN.company.name()}`,
    viewType: 'grid',
    isActive: true,
    availableSchedule: [
      { day: 1, start: '10:00', end: '14:00' },
      { day: 2, start: '10:00', end: '14:00' },
    ],
    restaurantId: restaurant.id,
  };

  const secUdBody = {
    internalName: `Updated Menu Section ${fakerEN.company.name()}`,
    publishedName: `Updated Published Menu Section ${fakerEN.company.name()}`,
    availableSchedule,
  };

  const sections = await commonTest({
    path: MENU_SECTIONS,
    bodyCreate: secCrBody,
    bodyUpdate: secUdBody,
    activate: false,
  });

  // --------------- Create and test Menu Item Option ---------------

  console.log('Testing Menu Item Options no gradient...');
  const optionCrBody = {
    internalName: `Option ${fakerEN.company.name()}`,
    publishedName: `Published Option ${fakerEN.company.name()}`,
    description: `Description for option ${fakerEN.company.name()}`,
    basePrice: 3,
    isActive: true,
    restaurantId: restaurant.id,
  };

  const optionUdBody = {
    internalName: `Updated Option ${fakerEN.company.name()}`,
    publishedName: `Updated Published Option ${fakerEN.company.name()}`,
    description: `Updated description for option ${fakerEN.company.name()}`,
    basePrice: 4,
    ingredientIds: [ingredients.id],
  };

  const options = await commonTest({
    path: MENU_ITEM_OPTIONS,
    bodyCreate: optionCrBody,
    bodyUpdate: optionUdBody,
    activate: false,
  });
  optionUdBody.internalName = `Updated Option ${fakerEN.company.name()}`;
  optionUdBody.publishedName = `Updated Published Option ${fakerEN.company.name()}`;

  optionCrBody.menuItemOptionGroupsOfOption = [{ groupId: groups.id, price: 28 }];
  optionUdBody.menuItemOptionGroupsOfOption = [{ groupId: groups.id, price: 28 }];

  const options1 = await commonTest({
    path: MENU_ITEMS_AND_OPTIONS,
    bodyCreate: { ...optionCrBody, type: 'option' },
    bodyUpdate: { ...optionUdBody, type: 'option' },
    activate: false,
    params: { restaurantId: restaurant.id },
    paramsDetail: { type: 'option' },
    paramsUpdate: { type: 'option' },
  });

  console.log('Testing Menu Item Options with gradient...');
  const optionCrBody2 = {
    internalName: `Option ${fakerEN.company.name()}`,
    publishedName: `Published Option ${fakerEN.company.name()}`,
    description: `Description for option ${fakerEN.company.name()}`,
    basePrice: 3,
    isActive: true,
    ingredientIds: [ingredients.id],
    restaurantId: restaurant.id,
  };

  const optionUdBody2 = {
    internalName: `Updated Option ${fakerEN.company.name()}`,
    publishedName: `Updated Published Option ${fakerEN.company.name()}`,
    description: `Updated description for option ${fakerEN.company.name()}`,
    basePrice: 4,
    ingredientIds: [],
  };

  const options2 = await commonTest({
    path: MENU_ITEM_OPTIONS,
    bodyCreate: optionCrBody2,
    bodyUpdate: optionUdBody2,
    activate: false,
  });
  optionUdBody2.internalName = `Updated Option ${fakerEN.company.name()}`;
  optionUdBody2.publishedName = `Updated Published Option ${fakerEN.company.name()}`;

  optionUdBody2.menuItemOptionGroupsOfItem = [{ id: groups.id, position: 0 }];
  optionUdBody2.menuSectionIds = [{ sectionId: sections.id, price: 18 }];
  optionUdBody2.imageUrls = [
    'https://anhbeodev.s3.ap-southeast-1.amazonaws.com/menu-items/1748006008729-e98a555a-8b57-40e9-b3b3-693b3120af81.jpeg',
  ];

  const options3 = await commonTest({
    path: MENU_ITEMS_AND_OPTIONS,
    bodyCreate: { ...optionCrBody2, type: 'option' },
    bodyUpdate: { ...optionUdBody2, type: 'item' },
    activate: false,
    params: { restaurantId: restaurant.id },
    paramsDetail: { type: 'option' },
    paramsUpdate: { type: 'option' },
  });

  // --------------- Create and test Menu Item Option Group 2 ---------------

  console.log('Testing Menu Item Option Groups 2...');
  const grCrBody2 = {
    internalName: `Option Group ${fakerEN.company.name()}`,
    publishedName: `Published Option Group ${fakerEN.company.name()}`,
    rule: 'option',
    type: 'add_on_item',
    restaurantId: restaurant.id,
    menuItemOptionIds: [{ id: options.id, position: 0, price: 1 }],
  };

  const grUdBody2 = {
    internalName: `Updated Option Group ${fakerEN.company.name()}`,
    publishedName: `Updated Published Option Group ${fakerEN.company.name()}`,
    rule: 'mandatory_fixed',
    type: 'add_on_item',
    fixedAmount: 2,
    menuItemOptionIds: [],
  };

  const groups2 = await commonTest({
    path: MENU_ITEM_OPTION_GROUPS,
    bodyCreate: grCrBody2,
    bodyUpdate: grUdBody2,
    activate: false,
  });

  // --------------- Create and test Menu Item ---------------
  // Create and test Menu Item
  console.log('Testing Menu Items without gradient...');
  const itemCrBody = {
    internalName: `Menu Item ${fakerEN.company.name()}`,
    publishedName: `Published Menu Item ${fakerEN.company.name()}`,
    description: `Description for menu item ${fakerEN.company.name()}`,
    basePrice: 10,
    restaurantId: restaurant.id,
    isActive: true,
    imageUrls: [
      'https://anhbeodev.s3.ap-southeast-1.amazonaws.com/menu-items/1748006008729-e98a555a-8b57-40e9-b3b3-693b3120af81.jpeg',
    ],
  };

  const itemUdBody = {
    internalName: `Updated Menu Item ${fakerEN.company.name()}`,
    publishedName: `Updated Published Menu Item ${fakerEN.company.name()}`,
    description: `Updated description for menu item ${fakerEN.company.name()}`,
    basePrice: 11,
    ingredientIds: [ingredients.id],
    menuItemOptionGroupIds: [{ id: groups.id, position: 0 }],
  };

  const items = await commonTest({
    path: MENU_ITEMS,
    bodyCreate: itemCrBody,
    bodyUpdate: itemUdBody,
    activate: false,
  });

  itemUdBody.internalName = `Updated Menu Item ${fakerEN.company.name()}`;
  itemUdBody.publishedName = `Updated Published Menu Item ${fakerEN.company.name()}`;
  itemUdBody.menuItemOptionGroupsOfItem = [{ id: groups.id, position: 0 }];
  itemUdBody.menuSectionIds = [{ sectionId: sections.id, price: 18 }];
  itemUdBody.imageUrls = [
    'https://anhbeodev.s3.ap-southeast-1.amazonaws.com/menu-items/1748006008729-e98a555a-8b57-40e9-b3b3-693b3120af81.jpeg',
  ];
  delete itemUdBody.menuItemOptionGroupIds;

  const items1 = await commonTest({
    path: MENU_ITEMS_AND_OPTIONS,
    bodyCreate: { ...itemCrBody, type: 'item' },
    bodyUpdate: { ...itemUdBody, type: 'item' },
    activate: false,
    params: { restaurantId: restaurant.id },
    paramsDetail: { type: 'item' },
    paramsUpdate: { type: 'item' },
  });

  // Create and test Menu Item
  console.log('Testing Menu Items with gradient...');
  const itemCrBody2 = {
    internalName: `Menu Item ${fakerEN.company.name()}`,
    publishedName: `Published Menu Item ${fakerEN.company.name()}`,
    description: `Description for menu item ${fakerEN.company.name()}`,
    basePrice: 10,
    isActive: true,
    ingredientIds: [ingredients.id],
    menuItemOptionGroupIds: [{ id: groups.id, position: 0 }],
    restaurantId: restaurant.id,
    imageUrls: [
      'https://anhbeodev.s3.ap-southeast-1.amazonaws.com/menu-items/1748006008729-e98a555a-8b57-40e9-b3b3-693b3120af81.jpeg',
    ],
  };

  const itemUdBody2 = {
    internalName: `Updated Menu Item ${fakerEN.company.name()}`,
    publishedName: `Updated Published Menu Item ${fakerEN.company.name()}`,
    description: `Updated description for menu item ${fakerEN.company.name()}`,
    basePrice: 11,
    ingredientIds: [],
    menuItemOptionGroupIds: [],
  };

  const items2 = await commonTest({
    path: MENU_ITEMS,
    bodyCreate: itemCrBody2,
    bodyUpdate: itemUdBody2,
    activate: false,
  });

  delete itemCrBody2.menuItemOptionGroupIds;

  itemUdBody2.internalName = `Updated Menu Item ${fakerEN.company.name()}`;
  itemUdBody2.publishedName = `Updated Published Menu Item ${fakerEN.company.name()}`;
  itemUdBody2.menuItemOptionGroupsOfOption = [{ groupId: groups.id, price: 28 }];
  delete itemUdBody2.menuItemOptionGroupIds;

  const items3 = await commonTest({
    path: MENU_ITEMS_AND_OPTIONS,
    bodyCreate: { ...itemCrBody2, type: 'item' },
    bodyUpdate: { ...itemUdBody2, type: 'option' },
    activate: false,
    params: { restaurantId: restaurant.id },
    paramsDetail: { type: 'item' },
    paramsUpdate: { type: 'item' },
  });
  // --------------- Create and test Menu Section 2 ---------------
  console.log('Testing Menu Sections 2...');
  const secCrBody2 = {
    internalName: `Menu Section ${fakerEN.company.name()}`,
    publishedName: `Published Menu Section ${fakerEN.company.name()}`,
    viewType: 'spotlight',
    isActive: true,
    availableSchedule: [
      { day: 1, start: '10:00', end: '14:00' },
      { day: 2, start: '10:00', end: '14:00' },
    ],
    menuItemIds: [],
    restaurantId: restaurant.id,
  };

  const secUdBody2 = {
    internalName: `Updated Menu Section ${fakerEN.company.name()}`,
    publishedName: `Updated Published Menu Section ${fakerEN.company.name()}`,
    availableSchedule,
    menuItemIds: [
      { id: items.id, position: 0, price: 12 },
      { id: items2.id, position: 1, price: null },
    ],
  };

  const sections2 = await commonTest({
    path: MENU_SECTIONS,
    bodyCreate: secCrBody2,
    bodyUpdate: secUdBody2,
    activate: false,
  });

  // --------------- Create and test Menu ---------------
  console.log('Testing Menus 1...');
  const menuCrBody = {
    name: `Menu ${fakerEN.company.name()}`,
    restaurantId: restaurant.id,
  };

  const menuUdBody = {
    name: `Updated Menu ${fakerEN.company.name()}`,
    menuSectionIds: [
      { id: sections.id, position: 0 },
      { id: sections2.id, position: 1 },
    ],
  };

  const menu = await commonTest({ path: MENUS, bodyCreate: menuCrBody, bodyUpdate: menuUdBody });

  console.log('Testing Menus 2...');
  const menuCrBody2 = {
    name: `Menu ${fakerEN.company.name()}`,
    restaurantId: restaurant.id,
    menuSectionIds: [
      { id: sections.id, position: 0 },
      { id: sections2.id, position: 1 },
    ],
  };

  const menuUdBody2 = {
    name: `Updated Menu ${fakerEN.company.name()}`,
    menuSectionIds: [
      { id: sections.id, position: 0 },
      { id: sections2.id, position: 1 },
    ],
  };

  const menu2 = await commonTest({ path: MENUS, bodyCreate: menuCrBody2, bodyUpdate: menuUdBody2 });

  return {
    restaurant,
    menu,
    menu2,
    items,
    items2,
    sections,
    sections2,
    options,
    options1,
    options2,
    options3,
    groups,
    groups2,
    ingredients,
  };
};

const main = async () => {
  try {
    const defaultPassword = '123456';
    const adminLogin = { email: '<EMAIL>', password: 'ChangeMe123!' };
    const emailMerchant = `${faker.internet.username()}@gmail.com`;
    const merchantUserBody = {
      email: emailMerchant,
      firstName: fakerEN.person.firstName(),
      lastName: fakerEN.person.lastName(),
      password: defaultPassword,
    };

    const res = await post('/admin/auth/login', adminLogin);
    api.defaults.headers.Cookie = `access_token=${res.accessToken}`;
    console.log('---------- Login as admin ----------');
    const merchantAccount = await post(MERCHANT_ACCOUNTS, { name: `Merchant Account ${fakerEN.company.name()}` });
    const accountUpdateBody = { name: `Updated Merchant Account ${fakerEN.company.name()}` };
    await commonTest({ path: MERCHANT_ACCOUNTS, itemId: merchantAccount.id, bodyUpdate: accountUpdateBody });

    const merchantUser = await post(MERCHANT_USERS, merchantUserBody);
    const ownerBody = { merchantUserId: merchantUser.id, merchantAccountId: merchantAccount.id };
    console.log('Email', JSON.stringify({ email: merchantUser.email, password: defaultPassword }));
    await post(`${MERCHANT_ACCOUNTS}/assign-owner`, ownerBody);

    const merchantUpdateBody = {
      firstName: fakerEN.person.firstName(),
      lastName: fakerEN.person.lastName(),
      password: defaultPassword,
    };
    await commonTest({ path: MERCHANT_USERS, itemId: merchantUser.id, bodyUpdate: merchantUpdateBody, ban: true });

    const tagCrBody = { name: `Tag ${fakerEN.company.name()}` };
    const tagUdBody = { name: `Updated Tag ${fakerEN.company.name()}` };

    const tag = await commonTest({
      path: RESTAURANT_TAGS,
      bodyCreate: tagCrBody,
      bodyUpdate: tagUdBody,
      activate: false,
    });

    // await runTestAllCase(merchantAccount, tag);

    const resLogin = await post('/auth/merchant-user/login', { email: emailMerchant, password: defaultPassword });
    api.defaults.headers.Cookie = `access_token=${resLogin.accessToken}`;
    console.log('merchant accessToken: ', resLogin.accessToken);

    console.log('---------- Login as merchant ----------');

    const {
      restaurant,
      menu,
      menu2,
      items,
      items2,
      sections,
      sections2,
      options,
      options1,
      options2,
      options3,
      groups,
      groups2,
      ingredients,
    } = await runTestAllCase(merchantAccount, tag);

    const isNotiStaff = true;
    const token =
      'dDyiYEInPsJ8NX00UAZIj3:APA91bFDm4XB7GRui85QeF_YBzYSd1peyngplLuDzbN25HPQ3J9IVgzirjWqmPlEhhhSfNk3t-iFMON3Bq2Jp2MxbGaZcrMsL0muEsHlgJmbYDJpMONqbm8';

    if (isNotiStaff) {
      // Test staff fcm token
      const resLoginStaff = await post('/auth/merchant-staff/login', {
        username: restaurant.merchantStaff.username,
        password: defaultPassword,
      });
      api.defaults.headers.Cookie = `access_token=${resLoginStaff.accessToken}`;
      console.log('staff accessToken: ', resLoginStaff.accessToken);
      console.log('---------- Login as staff ----------');
      await post('/staff/fcm-tokens', {
        token: token,
        deviceId: 'device-123',
        platform: 'web',
      });
    }

    // Test user cart functionality

    // fake flow register user
    const phoneBody = {
      phone: '*********',
      phoneCountryCode: '+84',
    };
    const resSendOtp = await post('/auth/user/send-phone-otp', phoneBody);
    const { phoneVerificationToken } = await post('/auth/user/verify-phone-otp', {
      ...phoneBody,
      otp: resSendOtp.data.code,
    });
    const email = '<EMAIL>';
    const resEmailOtp = await post('/auth/user/send-email-otp', { email, phoneVerificationToken });
    const resVerifyEmailOtp = await post('/auth/user/verify-email-otp', {
      email,
      otp: resEmailOtp.data.code,
      phoneVerificationToken,
    });

    console.log('user accessToken: ', resVerifyEmailOtp.accessToken);

    api.defaults.headers.Cookie = `access_token=${resVerifyEmailOtp.accessToken}`;

    await post('/auth/user/update-profile', { firstName: 'John', lastName: 'Doe' });
    await post('/auth/user/temporary', {
      latitude: 40.7128,
      longitude: -74.006,
      fullAddress: '123 Main St, New York, NY 10001, USA',
      addressType: 'string',
      addressLabel: 'Home',
      isDefault: false,
      placeId: 'string',
    });

    console.log('---------- Login as user ----------');

    if (!isNotiStaff) {
      // Test user fcm token
      await post('/user/fcm-tokens', {
        token: token,
        deviceId: 'device-123',
        platform: 'web',
      });
    }

    const paymentMethod = true ? 'cash' : 'credit_card';
    const numberOfOrders = 1;

    let paymentCards;
    if (paymentMethod === 'credit_card') {
      paymentCards = await get(`/user/payment-cards`);
      if (!paymentCards?.length) {
        throw new Error('No payment cards found');
      }
    }
    console.log('items.id', items.id);
    console.log('sections2.id', sections2.id);
    const baseCrOrderBody = {
      restaurantId: restaurant.id,
      menuItemId: items.id,
      menuSectionId: sections2.id,
      groupOptions: [
        {
          optionGroupId: groups.id,
          options: [{ id: options1.id, amount: 1 }],
        },
        // {
        //   optionGroupId: groups.id,
        //   options: [{ id: options3.id, amount: 2 }],
        // },
      ],
    };
    for (let i = 0; i < numberOfOrders; i++) {
      const cart = await post(`/user/carts/add`, {
        ...baseCrOrderBody,
        amount: 2,
      });
      await post(`/user/carts/add`, {
        restaurantId: restaurant.id,
        menuItemId: items.id,
        menuSectionId: sections2.id,
        amount: 3,
        groupOptions: [],
      });
      await post(`/user/carts/add`, {
        ...baseCrOrderBody,
        amount: 4,
      });

      await post(`/user/orders`, {
        cartId: cart.id,
        paymentMethod,
        paymentCardId: paymentCards?.[0].id,
      });
    }

    console.log('All restaurant management module tests completed successfully!');
  } catch (e) {
    console.error(e.config?.method, e?.response?.data ?? e.message ?? 'Unknown error');
  }
};

main();
