import { CartsModule } from '@/modules/carts/carts.module';
import { MenuItemsModule } from '@/modules/menu-items/menu-items.module';
import { OnepayModule } from '@/modules/onepay/onepay.module';
import { PaymentCardModule } from '@/modules/payment-card/payment-card.module';
import { UserAddressesModule } from '@/modules/user-addresses/user-addresses.module';
import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RestaurantReviewsModule } from '../restaurant-reviews/restaurant-reviews.module';
import { UsersModule } from '../users/users.module';
import { FakeNotificationController } from './controllers/fake-notification.controller';
import { OrdersMicroserviceController } from './controllers/orders.microservice.controller';
import { StaffOrdersController } from './controllers/staff-orders.controller';
import { UserOrdersController } from './controllers/user-orders.controller';
import { OrderCustomer } from './entities/order-customer.entity';
import { Order } from './entities/order.entity';
import { FakeNotificationService } from './fake-notification.service';
import { OrdersService } from './orders.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Order, OrderCustomer]),
    CartsModule,
    forwardRef(() => OnepayModule),
    forwardRef(() => PaymentCardModule),
    MenuItemsModule,
    UserAddressesModule,
    UsersModule,
    RestaurantReviewsModule,
  ],
  controllers: [
    UserOrdersController,
    StaffOrdersController,
    OrdersMicroserviceController,
    ...(process.env.ENABLE_FAKE_NOTIFICATION === 'true' ? [FakeNotificationController] : []),
  ],
  providers: [OrdersService, FakeNotificationService],
  exports: [OrdersService, FakeNotificationService],
})
export class OrdersModule {}
